#!/usr/bin/env python3
"""
Dependency installer for French Stream Movie Extractor
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_pip():
    """Check if pip is available and install if needed"""
    try:
        import pip
        return True
    except ImportError:
        print("📦 Installing pip...")
        try:
            subprocess.check_call([sys.executable, "-m", "ensurepip", "--upgrade"])
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install pip. Please install pip manually.")
            return False

def main():
    """Main installation function"""
    print("🚀 French Stream Movie Extractor - Dependency Installer")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} detected")
    
    # Check and install pip
    if not check_pip():
        return False
    
    print("✅ pip is available")
    
    # List of required packages
    packages = [
        "selenium",
        "beautifulsoup4", 
        "requests",
        "lxml",
        "webdriver-manager",
        "click",
        "colorama",
        "tqdm",
        "fake-useragent"
    ]
    
    print(f"\n📦 Installing {len(packages)} packages...")
    
    failed_packages = []
    
    for i, package in enumerate(packages, 1):
        print(f"  [{i}/{len(packages)}] Installing {package}...", end=" ")
        
        if install_package(package):
            print("✅")
        else:
            print("❌")
            failed_packages.append(package)
    
    # Summary
    print(f"\n{'='*60}")
    if failed_packages:
        print(f"⚠️  Installation completed with {len(failed_packages)} failures:")
        for package in failed_packages:
            print(f"   - {package}")
        print(f"\n💡 Try installing failed packages manually:")
        print(f"   pip install {' '.join(failed_packages)}")
    else:
        print("🎉 All dependencies installed successfully!")
    
    print(f"\n🔧 Next steps:")
    print(f"   1. Run: python french_stream_extractor.py --help")
    print(f"   2. Test: python example_usage.py")
    
    return len(failed_packages) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
