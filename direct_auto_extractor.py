#!/usr/bin/env python3
"""
DIRECT AUTO EXTRACTOR - VERSION DIRECTE
Extraction automatique immédiate sans complexité
"""

print("🚀 DIRECT AUTO EXTRACTOR - DÉMARRAGE")
print("=" * 50)

import sys
import time

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    import json
    import re
    
    print("✅ Modules importés avec succès")
    
except ImportError as e:
    print(f"❌ Erreur import: {e}")
    sys.exit(1)

def run_direct_extraction():
    """Extraction directe automatique"""
    print("🎯 LANCEMENT EXTRACTION DIRECTE")
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    driver = None
    
    try:
        # Configuration Chrome simple
        print("🔧 Configuration Chrome...")
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        print("🚀 Lancement Chrome...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ Chrome lancé avec succès")
        
        # Navigation
        print(f"🌐 Navigation vers: {url}")
        driver.get(url)
        time.sleep(5)
        
        print(f"📄 Page chargée: {driver.title}")
        
        # Attente Cloudflare
        print("⏳ Attente Cloudflare (30 secondes max)...")
        start_time = time.time()
        
        while time.time() - start_time < 30:
            try:
                title = driver.title.lower()
                if not any(word in title for word in ["cloudflare", "checking", "moment"]):
                    body = driver.find_element(By.TAG_NAME, "body")
                    if len(body.text) > 1000:
                        print("✅ Contenu accessible!")
                        break
            except:
                pass
            time.sleep(2)
        
        # Recherche UQload
        print("🔍 Recherche UQload...")
        uqload_found = False
        
        try:
            # Recherche par texte
            elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload')]")
            for element in elements:
                if element.is_displayed():
                    print(f"🎯 UQload trouvé: {element.text[:30]}")
                    element.click()
                    uqload_found = True
                    time.sleep(5)
                    break
        except:
            pass
        
        if not uqload_found:
            print("⚠️ UQload non trouvé - Extraction directe")
        
        # Recherche play
        print("▶️ Recherche bouton play...")
        try:
            # Clic centre écran
            center_x = driver.execute_script("return window.innerWidth / 2;")
            center_y = driver.execute_script("return window.innerHeight / 2;")
            
            driver.execute_script(f"""
                var element = document.elementFromPoint({center_x}, {center_y});
                if (element) element.click();
            """)
            
            print("✅ Clic play effectué")
            time.sleep(8)
        except:
            print("⚠️ Play non trouvé")
        
        # Extraction vidéo
        print("🎥 Extraction vidéos...")
        video_urls = []
        
        # Méthode 1: Éléments video
        try:
            videos = driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"📹 Video trouvée: {src[:60]}...")
        except:
            pass
        
        # Méthode 2: Regex simple
        try:
            page_source = driver.page_source
            patterns = [
                r'"file":\s*"([^"]+\.mp4[^"]*)"',
                r'"src":\s*"([^"]+\.mp4[^"]*)"',
                r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"📹 Regex trouvée: {match[:60]}...")
        except:
            pass
        
        # Résultats
        if video_urls:
            print("\n" + "="*60)
            print("🎉 EXTRACTION AUTOMATIQUE RÉUSSIE!")
            print("="*60)
            
            print(f"🎯 LIEN VIDÉO PRINCIPAL:")
            print(f"   {video_urls[0]}")
            
            if len(video_urls) > 1:
                print(f"\n📹 TOUS LES LIENS ({len(video_urls)}):")
                for i, url in enumerate(video_urls, 1):
                    print(f"   {i}. {url}")
            
            # Sauvegarde
            result = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'url': url,
                'video_links': video_urls,
                'primary_link': video_urls[0],
                'success': True
            }
            
            with open('direct_auto_results.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Résultats sauvegardés: direct_auto_results.json")
            print("="*60)
            
            return True
        else:
            print("\n❌ AUCUNE VIDÉO TROUVÉE")
            return False
    
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        return False
    
    finally:
        if driver:
            print("\n⏳ Fermeture dans 10 secondes...")
            time.sleep(10)
            driver.quit()

if __name__ == "__main__":
    print("🎯 EXTRACTION 100% AUTOMATIQUE")
    print("🔥 VERSION DIRECTE ET SIMPLE")
    print()
    
    success = run_direct_extraction()
    
    if success:
        print("\n🏆 MISSION AUTOMATIQUE ACCOMPLIE!")
    else:
        print("\n❌ Extraction automatique échouée")
    
    print("\nFIN DU PROGRAMME")
