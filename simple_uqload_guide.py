#!/usr/bin/env python3
"""
Guide simple pour extraire le lien UQload
Sans dépendances externes
"""

import webbrowser
import time

def guide_manuel_complet():
    """Guide manuel étape par étape"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎬 GUIDE MANUEL - EXTRACTION UQLOAD")
    print("=" * 60)
    print()
    print("🎯 OBJECTIF: Trouver UQload → Cliquer Play → Extraire lien vidéo")
    print()
    print("📋 ÉTAPES À SUIVRE:")
    print()
    print("1. 🌐 Ouvrir le site du film")
    print("2. ⏳ Attendre que Cloudflare vous laisse passer")
    print("3. 🔍 Chercher le bouton 'UQload' (encerclé en rouge)")
    print("4. 🖱️ Cliquer sur 'UQload'")
    print("5. ▶️ Cliquer sur le bouton PLAY bleu")
    print("6. 🎥 Attendre que la vidéo se charge")
    print("7. 🔗 Clic droit sur la vidéo → 'Copier l'adresse de la vidéo'")
    print()
    
    input("📱 Appuyez sur Entrée pour ouvrir le site...")
    
    # Ouvrir le site
    print("🌐 Ouverture du site...")
    webbrowser.open(url)
    
    print("✅ Site ouvert dans votre navigateur!")
    print()
    print("🔍 COMMENT TROUVER LE BOUTON UQLOAD:")
    print("   ┌─────────────────────────────────────────┐")
    print("   │ • Cherchez dans la zone des lecteurs   │")
    print("   │ • Peut être dans un onglet 'Serveurs'  │")
    print("   │ • Parfois il faut scroller vers le bas │")
    print("   │ • Bouton peut dire 'UQload' ou 'UQ'    │")
    print("   │ • Souvent encerclé en rouge            │")
    print("   └─────────────────────────────────────────┘")
    print()
    print("▶️ APRÈS AVOIR CLIQUÉ SUR UQLOAD:")
    print("   ┌─────────────────────────────────────────┐")
    print("   │ • Une nouvelle page/iframe s'ouvre     │")
    print("   │ • Vous voyez un lecteur vidéo           │")
    print("   │ • Il y a un gros bouton PLAY bleu      │")
    print("   │ • Cliquez sur PLAY pour démarrer       │")
    print("   └─────────────────────────────────────────┘")
    print()
    print("🎥 POUR EXTRAIRE LE LIEN VIDÉO:")
    print("   ┌─────────────────────────────────────────┐")
    print("   │ • Clic droit sur la vidéo qui joue     │")
    print("   │ • 'Copier l'adresse de la vidéo'       │")
    print("   │ • OU 'Copy video URL'                  │")
    print("   │ • Le lien sera dans votre presse-papier│")
    print("   └─────────────────────────────────────────┘")
    print()
    
    # Attendre que l'utilisateur termine
    print("⏳ Prenez votre temps pour suivre les étapes...")
    print()
    video_link = input("🔗 Collez ici le lien vidéo que vous avez copié: ").strip()
    
    if video_link and 'http' in video_link:
        print()
        print("🎉 EXCELLENT! Lien vidéo reçu!")
        print("=" * 50)
        print(f"🎯 LIEN VIDÉO: {video_link}")
        print("=" * 50)
        
        # Sauvegarder le lien
        try:
            with open('video_link_extracted.txt', 'w', encoding='utf-8') as f:
                f.write(f"Film: Dis-moi juste que tu m'aimes\n")
                f.write(f"URL du film: {url}\n")
                f.write(f"Lien vidéo UQload: {video_link}\n")
                f.write(f"Date d'extraction: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Méthode: Extraction manuelle\n")
            
            print("💾 Lien sauvegardé dans 'video_link_extracted.txt'")
            
            # Analyser le lien
            if '.mp4' in video_link.lower():
                print("📹 Format détecté: MP4 (vidéo directe)")
            elif '.m3u8' in video_link.lower():
                print("📹 Format détecté: M3U8 (streaming adaptatif)")
            else:
                print("📹 Format: Lien vidéo streaming")
            
            return video_link
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde: {e}")
            return video_link
    
    else:
        print("❌ Aucun lien valide fourni")
        print("💡 Assurez-vous de:")
        print("   • Faire clic droit sur la vidéo")
        print("   • Sélectionner 'Copier l'adresse de la vidéo'")
        print("   • Coller le lien complet ici")
        return None

def guide_recherche_uqload():
    """Guide pour trouver UQload spécifiquement"""
    print("\n🔍 GUIDE SPÉCIALISÉ - TROUVER UQLOAD")
    print("=" * 50)
    print()
    print("🎯 UQload peut se trouver dans plusieurs endroits:")
    print()
    print("📍 EMPLACEMENTS POSSIBLES:")
    print("   1. 📺 Section 'Lecteurs' ou 'Players'")
    print("   2. 🗂️ Onglet 'Serveurs' ou 'Servers'")
    print("   3. 📋 Liste déroulante des hébergeurs")
    print("   4. 🔘 Boutons sous le titre du film")
    print("   5. ⬇️ En bas de page dans 'Liens de téléchargement'")
    print()
    print("👀 À QUOI RESSEMBLE LE BOUTON UQLOAD:")
    print("   • Texte: 'UQload', 'UQ', ou logo UQload")
    print("   • Couleur: Souvent bleu ou vert")
    print("   • Forme: Bouton rectangulaire")
    print("   • Position: Avec d'autres hébergeurs")
    print()
    print("🔴 INDICES VISUELS:")
    print("   • Peut être encerclé en rouge (comme vous l'avez dit)")
    print("   • Souvent à côté de 'Streamtape', 'Doodstream', etc.")
    print("   • Peut avoir une icône de lecture ▶️")
    print()
    print("💡 SI VOUS NE TROUVEZ PAS UQLOAD:")
    print("   • Essayez les autres hébergeurs disponibles")
    print("   • Cherchez 'Streamtape', 'Doodstream', 'Mixdrop'")
    print("   • La méthode d'extraction est la même")
    print()

def autres_hebergeurs():
    """Guide pour autres hébergeurs"""
    print("\n🌐 AUTRES HÉBERGEURS DISPONIBLES")
    print("=" * 40)
    print()
    print("Si UQload n'est pas disponible, essayez:")
    print()
    print("1. 📺 STREAMTAPE:")
    print("   • Cliquez sur 'Streamtape'")
    print("   • Attendez 5 secondes")
    print("   • Cliquez sur 'Continue' ou 'Continuer'")
    print("   • Clic droit sur vidéo → Copier l'adresse")
    print()
    print("2. 🎬 DOODSTREAM:")
    print("   • Cliquez sur 'Doodstream'")
    print("   • Cliquez sur le bouton play")
    print("   • Clic droit sur vidéo → Copier l'adresse")
    print()
    print("3. 🎥 MIXDROP:")
    print("   • Cliquez sur 'Mixdrop'")
    print("   • Attendez le chargement")
    print("   • Clic droit sur vidéo → Copier l'adresse")
    print()
    print("La méthode est similaire pour tous les hébergeurs!")

def main():
    """Fonction principale"""
    print("🎬 EXTRACTEUR UQLOAD - GUIDE COMPLET")
    print("=" * 60)
    print()
    print("Film: 'Dis-moi juste que tu m'aimes'")
    print("Objectif: Extraire le lien vidéo direct")
    print()
    print("Choisissez votre option:")
    print()
    print("1. 📋 Guide manuel complet (RECOMMANDÉ)")
    print("2. 🔍 Guide pour trouver UQload")
    print("3. 🌐 Autres hébergeurs disponibles")
    print("4. ❌ Quitter")
    print()
    
    try:
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == '1':
            video_link = guide_manuel_complet()
            if video_link:
                print(f"\n🏆 SUCCÈS! Lien vidéo extrait:")
                print(f"🎯 {video_link}")
                print("\n💡 Vous pouvez maintenant utiliser ce lien pour:")
                print("   • Regarder la vidéo directement")
                print("   • La télécharger avec un gestionnaire de téléchargement")
                print("   • L'ouvrir dans VLC ou un autre lecteur")
        
        elif choice == '2':
            guide_recherche_uqload()
            print("\nVoulez-vous maintenant suivre le guide complet? (o/n): ", end="")
            if input().lower().strip() in ['o', 'oui', 'y', 'yes']:
                guide_manuel_complet()
        
        elif choice == '3':
            autres_hebergeurs()
            print("\nVoulez-vous maintenant suivre le guide complet? (o/n): ", end="")
            if input().lower().strip() in ['o', 'oui', 'y', 'yes']:
                guide_manuel_complet()
        
        elif choice == '4':
            print("👋 Au revoir!")
        
        else:
            print("❌ Choix invalide")
    
    except KeyboardInterrupt:
        print("\n👋 Au revoir!")
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
