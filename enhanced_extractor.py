#!/usr/bin/env python3
"""
Enhanced French Stream Movie Extractor
Handles anti-bot protection and provides fallback methods
"""

import re
import json
import time
import random
import urllib.request
import urllib.parse
import urllib.error
from html.parser import HTMLParser
from typing import List, Dict, Optional

class AdvancedMovieLinkParser(HTMLParser):
    """Enhanced HTML parser with better link detection"""
    
    def __init__(self):
        super().__init__()
        self.reset_parser()
        
    def reset_parser(self):
        """Reset parser state"""
        self.title = ""
        self.download_links = []
        self.streaming_links = []
        self.meta_info = {}
        self.current_tag = ""
        self.current_attrs = {}
        self.in_title = False
        self.text_buffer = ""
        
    def handle_starttag(self, tag, attrs):
        self.current_tag = tag
        self.current_attrs = dict(attrs)
        
        # Title detection
        if tag in ['h1', 'title'] and any(cls in self.current_attrs.get('class', '') for cls in ['title', 'entry-title', 'post-title']):
            self.in_title = True
        elif tag == 'title':
            self.in_title = True
            
        # Link detection
        if tag == 'a':
            href = self.current_attrs.get('href', '')
            if href:
                self._check_download_link(href)
                
        # Iframe detection for streaming
        elif tag == 'iframe':
            src = self.current_attrs.get('src', '')
            if src:
                self._check_streaming_link(src)
                
        # Meta information
        elif tag == 'meta':
            self._extract_meta_info()
    
    def handle_data(self, data):
        if self.in_title:
            self.text_buffer += data.strip() + " "
        
        # Look for embedded links in text
        self._extract_text_links(data)
    
    def handle_endtag(self, tag):
        if tag in ['h1', 'title'] and self.in_title:
            self.in_title = False
            if self.text_buffer.strip() and not self.title:
                self.title = self.text_buffer.strip()
            self.text_buffer = ""
    
    def _check_download_link(self, href):
        """Check if link is a download link"""
        download_indicators = [
            'mega.nz', '1fichier.com', 'uptobox.com', 'mediafire.com',
            'rapidgator.net', 'turbobit.net', 'nitroflare.com',
            'download', 'dl', 'telecharger'
        ]
        
        if any(indicator in href.lower() for indicator in download_indicators):
            if href not in self.download_links:
                self.download_links.append(href)
    
    def _check_streaming_link(self, src):
        """Check if iframe is for streaming"""
        streaming_indicators = ['player', 'stream', 'embed', 'video']
        
        if any(indicator in src.lower() for indicator in streaming_indicators):
            if src not in self.streaming_links:
                self.streaming_links.append(src)
    
    def _extract_text_links(self, text):
        """Extract links from text content"""
        # URL patterns
        url_patterns = [
            r'https?://mega\.nz/[^\s"\'<>]+',
            r'https?://1fichier\.com/[^\s"\'<>]+',
            r'https?://uptobox\.com/[^\s"\'<>]+',
            r'https?://mediafire\.com/[^\s"\'<>]+',
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if match not in self.download_links:
                    self.download_links.append(match)
    
    def _extract_meta_info(self):
        """Extract metadata from meta tags"""
        name = self.current_attrs.get('name', '').lower()
        property_attr = self.current_attrs.get('property', '').lower()
        content = self.current_attrs.get('content', '')
        
        if content:
            if 'title' in name or 'title' in property_attr:
                if not self.title:
                    self.title = content
            elif 'description' in name:
                self.meta_info['description'] = content

class EnhancedFrenchStreamExtractor:
    """Enhanced extractor with anti-bot protection"""
    
    def __init__(self, timeout: int = 30):
        self.timeout = timeout
        self.session_headers = self._get_random_headers()
        
    def _get_random_headers(self):
        """Get randomized headers to avoid detection"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        
        return {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def extract_movie_info(self, url: str, retries: int = 3) -> Optional[Dict]:
        """Extract movie information with retry logic"""
        print(f"🔍 Extracting from: {url}")
        
        for attempt in range(retries):
            try:
                if attempt > 0:
                    print(f"   🔄 Retry attempt {attempt + 1}/{retries}")
                    time.sleep(random.uniform(2, 5))  # Random delay
                    self.session_headers = self._get_random_headers()  # New headers
                
                # Try different extraction methods
                movie_info = self._extract_with_urllib(url)
                
                if movie_info and (movie_info.get('download_links') or movie_info.get('streaming_links')):
                    print(f"   ✅ Successfully extracted on attempt {attempt + 1}")
                    return movie_info
                elif attempt == 0:
                    # Try alternative method on first failure
                    movie_info = self._extract_with_fallback(url)
                    if movie_info:
                        return movie_info
                        
            except urllib.error.HTTPError as e:
                if e.code == 403:
                    print(f"   ⚠️  Access denied (403) - attempt {attempt + 1}")
                    if attempt < retries - 1:
                        continue
                elif e.code == 404:
                    print(f"   ❌ Page not found (404)")
                    break
                else:
                    print(f"   ❌ HTTP Error {e.code} - attempt {attempt + 1}")
            except Exception as e:
                print(f"   ❌ Error on attempt {attempt + 1}: {e}")
        
        print(f"   ❌ Failed to extract after {retries} attempts")
        return None
    
    def _extract_with_urllib(self, url: str) -> Optional[Dict]:
        """Extract using urllib with enhanced headers"""
        req = urllib.request.Request(url, headers=self.session_headers)
        
        with urllib.request.urlopen(req, timeout=self.timeout) as response:
            html_content = response.read()
            
            # Handle different encodings
            try:
                html_content = html_content.decode('utf-8')
            except UnicodeDecodeError:
                html_content = html_content.decode('latin-1', errors='ignore')
        
        return self._parse_html_content(html_content, url)
    
    def _extract_with_fallback(self, url: str) -> Optional[Dict]:
        """Fallback extraction method"""
        print("   🔄 Trying fallback method...")
        
        # Try with minimal headers
        minimal_headers = {
            'User-Agent': 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)'
        }
        
        req = urllib.request.Request(url, headers=minimal_headers)
        
        try:
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                html_content = response.read().decode('utf-8', errors='ignore')
            
            return self._parse_html_content(html_content, url)
        except:
            return None
    
    def _parse_html_content(self, html_content: str, url: str) -> Dict:
        """Parse HTML content and extract information"""
        parser = AdvancedMovieLinkParser()
        
        try:
            parser.feed(html_content)
        except Exception as e:
            print(f"   ⚠️  Parser error: {e}")
        
        # Additional regex extraction
        additional_links = self._extract_links_regex(html_content)
        
        # Combine results
        all_download_links = list(set(parser.download_links + additional_links))
        
        # Clean and validate links
        all_download_links = [link for link in all_download_links if self._is_valid_link(link)]
        
        # Extract metadata
        quality = self._extract_quality(html_content)
        year = self._extract_year(html_content)
        
        movie_info = {
            'title': parser.title or self._extract_title_from_url(url),
            'url': url,
            'download_links': all_download_links,
            'streaming_links': parser.streaming_links,
            'quality': quality,
            'year': year,
            'extraction_method': 'urllib',
            'links_found': len(all_download_links) + len(parser.streaming_links)
        }
        
        return movie_info
    
    def _extract_links_regex(self, html_content: str) -> List[str]:
        """Extract links using comprehensive regex patterns"""
        links = []
        
        patterns = [
            r'https?://mega\.nz/(?:file/|#!)[A-Za-z0-9_-]+(?:[A-Za-z0-9_-]*)',
            r'https?://1fichier\.com/\?[A-Za-z0-9]+',
            r'https?://uptobox\.com/[A-Za-z0-9]+',
            r'https?://mediafire\.com/file/[A-Za-z0-9_-]+/[^/\s"\'<>]*',
            r'https?://rapidgator\.net/file/[A-Za-z0-9_-]+',
            r'https?://turbobit\.net/[A-Za-z0-9_-]+\.html',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            links.extend(matches)
        
        return list(set(links))
    
    def _is_valid_link(self, link: str) -> bool:
        """Validate if link is a proper download link"""
        if not link or len(link) < 10:
            return False
        
        # Check for valid domains
        valid_domains = [
            'mega.nz', '1fichier.com', 'uptobox.com', 'mediafire.com',
            'rapidgator.net', 'turbobit.net', 'nitroflare.com'
        ]
        
        return any(domain in link.lower() for domain in valid_domains)
    
    def _extract_quality(self, html_content: str) -> Optional[str]:
        """Extract video quality"""
        quality_patterns = [
            r'(\d{3,4}p)',
            r'(4K|UHD|HD|SD)',
            r'(BluRay|BDRip|DVDRip|WEBRip|HDTV|CAM|TS)'
        ]
        
        for pattern in quality_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_year(self, html_content: str) -> Optional[str]:
        """Extract movie year"""
        year_patterns = [
            r'\b(19[5-9]\d|20[0-2]\d)\b',  # Years from 1950 to 2029
            r'\((\d{4})\)',  # Year in parentheses
        ]
        
        years = []
        for pattern in year_patterns:
            matches = re.findall(pattern, html_content)
            years.extend([int(year) for year in matches if 1950 <= int(year) <= 2030])
        
        if years:
            return str(max(years))  # Return most recent year
        
        return None
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract title from URL"""
        try:
            path = urllib.parse.urlparse(url).path
            filename = path.split('/')[-1]
            
            # Clean filename
            title = re.sub(r'^\d+-', '', filename)  # Remove leading numbers
            title = title.replace('-', ' ').replace('_', ' ')
            title = re.sub(r'\.(html|php)$', '', title, re.IGNORECASE)
            
            return title.title()
        except:
            return "Unknown Title"
    
    def print_results(self, movie_info: Dict):
        """Print detailed extraction results"""
        if not movie_info:
            print("❌ No results to display.")
            return
        
        print(f"\n{'='*70}")
        print(f"🎬 MOVIE: {movie_info['title']}")
        print(f"{'='*70}")
        print(f"🔗 URL: {movie_info['url']}")
        
        if movie_info.get('year'):
            print(f"📅 Year: {movie_info['year']}")
        if movie_info.get('quality'):
            print(f"🎬 Quality: {movie_info['quality']}")
        
        print(f"🔧 Extraction Method: {movie_info.get('extraction_method', 'Unknown')}")
        print(f"📊 Total Links Found: {movie_info.get('links_found', 0)}")
        
        download_links = movie_info.get('download_links', [])
        if download_links:
            print(f"\n📥 DOWNLOAD LINKS ({len(download_links)} found):")
            for i, link in enumerate(download_links, 1):
                domain = urllib.parse.urlparse(link).netloc
                print(f"  {i}. [{domain}] {link}")
        else:
            print("\n📥 No download links found")
        
        streaming_links = movie_info.get('streaming_links', [])
        if streaming_links:
            print(f"\n📺 STREAMING LINKS ({len(streaming_links)} found):")
            for i, link in enumerate(streaming_links, 1):
                print(f"  {i}. {link}")
        
        print(f"\n{'='*70}")

def main():
    """Main function"""
    print("🎬 Enhanced French Stream Movie Extractor")
    print("=" * 50)
    
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    extractor = EnhancedFrenchStreamExtractor(timeout=30)
    movie_info = extractor.extract_movie_info(url, retries=3)
    
    if movie_info:
        extractor.print_results(movie_info)
        
        # Save results
        with open("enhanced_extraction_results.json", "w", encoding="utf-8") as f:
            json.dump([movie_info], f, indent=2, ensure_ascii=False)
        print("💾 Results saved to enhanced_extraction_results.json")
        
        # Return primary download link
        download_links = movie_info.get('download_links', [])
        if download_links:
            print(f"\n🎯 PRIMARY DOWNLOAD LINK:")
            print(f"   {download_links[0]}")
            return download_links[0]
    else:
        print("❌ Extraction failed completely")
        print("\n💡 Possible solutions:")
        print("   1. The site may require JavaScript (use full version with Selenium)")
        print("   2. The site structure may have changed")
        print("   3. Anti-bot protection may be active")
        print("   4. Try running: python install_dependencies.py")
        print("   5. Then run: python french_stream_extractor.py [URL]")
    
    return None

if __name__ == "__main__":
    main()
