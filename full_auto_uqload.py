#!/usr/bin/env python3
"""
EXTRACTEUR UQLOAD 100% AUTOMATIQUE
Trouve UQload → Clique → Clique Play → Extrait lien vidéo
SANS AUCUNE INTERVENTION MANUELLE
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re
import random

class FullAutoUQloadExtractor:
    """Extracteur 100% automatique pour UQload"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        self.video_links = []
        
    def setup_super_stealth_driver(self):
        """Configure un driver ultra-furtif"""
        print("🤖 Configuration du navigateur furtif...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        # Options anti-détection maximales
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        # User agents réalistes
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        # Désactiver l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Prefs avancées
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.media_stream": 1,
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts anti-détection ultra-avancés
            stealth_script = """
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr']});
            Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
            Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
            Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
            Object.defineProperty(screen, 'width', {get: () => 1920});
            Object.defineProperty(screen, 'height', {get: () => 1080});
            window.chrome = {runtime: {}};
            """
            self.driver.execute_script(stealth_script)
            
            # Taille de fenêtre réaliste
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur configuré avec succès")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration: {e}")
            return False
    
    def smart_wait_cloudflare(self, max_wait=45):
        """Attente intelligente de Cloudflare"""
        print("⏳ Attente intelligente de Cloudflare...")
        
        start_time = time.time()
        cloudflare_passed = False
        
        while time.time() - start_time < max_wait:
            try:
                current_title = self.driver.title.lower()
                current_url = self.driver.current_url.lower()
                
                # Vérifier les indicateurs Cloudflare
                if any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                    print(f"   🔄 Cloudflare actif: '{self.driver.title}'")
                    time.sleep(3)
                    continue
                
                # Vérifier si on a accès au contenu
                try:
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    if body and len(body.text) > 1000:  # Page avec contenu
                        print("   ✅ Cloudflare passé - contenu détecté!")
                        cloudflare_passed = True
                        break
                except:
                    pass
                
                time.sleep(2)
                
            except Exception as e:
                print(f"   ⚠️ Erreur vérification: {e}")
                time.sleep(2)
        
        if not cloudflare_passed:
            print("   ⚠️ Cloudflare timeout - continuons quand même")
        
        # Attente supplémentaire pour le chargement complet
        time.sleep(5)
        return True
    
    def find_uqload_with_ai(self):
        """Trouve UQload avec intelligence artificielle"""
        print("🧠 Recherche intelligente d'UQload...")
        
        # Stratégie 1: Sélecteurs CSS ultra-précis
        uqload_selectors = [
            'a[href*="uqload" i]',
            'button[onclick*="uqload" i]',
            '[data-server*="uqload" i]',
            '[data-video*="uqload" i]',
            '[data-name*="uqload" i]',
            '.server-item[data-server*="uqload" i]',
            '.player-server[data-server*="uqload" i]',
            'a[title*="uqload" i]',
            'button[title*="uqload" i]'
        ]
        
        for selector in uqload_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ UQload trouvé avec: {selector}")
                        return element
            except Exception as e:
                continue
        
        # Stratégie 2: Recherche textuelle avancée
        print("   🔍 Recherche textuelle...")
        
        # XPath pour texte contenant "uqload"
        xpath_selectors = [
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]",
            "//span[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]//parent::*",
            "//div[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]"
        ]
        
        for xpath in xpath_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ UQload trouvé avec XPath")
                        return element
            except:
                continue
        
        # Stratégie 3: Analyse de tous les éléments cliquables
        print("   🔍 Analyse exhaustive...")
        
        clickable_elements = self.driver.find_elements(By.CSS_SELECTOR, 
            "a, button, [onclick], [data-server], .server-item, .player-server, .btn, .link, [role='button']")
        
        for element in clickable_elements:
            try:
                if not (element.is_displayed() and element.is_enabled()):
                    continue
                
                # Vérifier tous les attributs
                text = element.text.lower()
                href = (element.get_attribute('href') or '').lower()
                onclick = (element.get_attribute('onclick') or '').lower()
                data_server = (element.get_attribute('data-server') or '').lower()
                data_video = (element.get_attribute('data-video') or '').lower()
                title = (element.get_attribute('title') or '').lower()
                class_name = (element.get_attribute('class') or '').lower()
                
                # Chercher "uqload" dans tous les attributs
                all_attrs = [text, href, onclick, data_server, data_video, title, class_name]
                
                if any('uqload' in attr for attr in all_attrs):
                    print(f"   ✅ UQload trouvé dans attributs: '{text}'")
                    return element
                    
            except Exception:
                continue
        
        print("   ❌ UQload non trouvé")
        return None
    
    def smart_click(self, element):
        """Clic intelligent sur un élément"""
        try:
            # Scroll vers l'élément
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", element)
            time.sleep(2)
            
            # Attendre que l'élément soit cliquable
            WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(element))
            
            # Méthode 1: Clic normal
            try:
                element.click()
                print("   ✅ Clic normal réussi")
                return True
            except:
                pass
            
            # Méthode 2: ActionChains
            try:
                ActionChains(self.driver).move_to_element(element).click().perform()
                print("   ✅ Clic ActionChains réussi")
                return True
            except:
                pass
            
            # Méthode 3: JavaScript
            try:
                self.driver.execute_script("arguments[0].click();", element)
                print("   ✅ Clic JavaScript réussi")
                return True
            except:
                pass
            
            # Méthode 4: Dispatch event
            try:
                self.driver.execute_script("""
                    var event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    arguments[0].dispatchEvent(event);
                """, element)
                print("   ✅ Clic dispatch event réussi")
                return True
            except:
                pass
            
            print("   ❌ Tous les types de clic ont échoué")
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur clic: {e}")
            return False
    
    def find_and_click_play_ultra(self):
        """Trouve et clique sur play avec méthodes ultra-avancées"""
        print("▶️ Recherche ultra-avancée du bouton play...")
        
        # Attendre le chargement du lecteur
        time.sleep(5)
        
        # Stratégie 1: Sélecteurs play spécifiques
        play_selectors = [
            '.play-button',
            '.video-play-button',
            '.player-play-button',
            '.btn-play',
            '#play-button',
            '[class*="play"]',
            'button[class*="play"]',
            '[onclick*="play"]',
            'button[onclick*="play"]',
            '.vjs-big-play-button',
            '.plyr__control--overlaid',
            '.play-overlay',
            '[aria-label*="play" i]',
            '[title*="play" i]'
        ]
        
        for selector in play_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ Bouton play trouvé: {selector}")
                        if self.smart_click(element):
                            return True
            except:
                continue
        
        # Stratégie 2: Recherche dans les iframes
        print("   🔍 Recherche dans les iframes...")
        
        iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
        for i, iframe in enumerate(iframes):
            try:
                print(f"   📺 Vérification iframe {i+1}/{len(iframes)}")
                self.driver.switch_to.frame(iframe)
                
                for selector in play_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed():
                                print(f"   ✅ Play trouvé dans iframe: {selector}")
                                if self.smart_click(element):
                                    self.driver.switch_to.default_content()
                                    return True
                    except:
                        continue
                
                self.driver.switch_to.default_content()
                
            except:
                self.driver.switch_to.default_content()
                continue
        
        # Stratégie 3: Clic sur la zone vidéo
        print("   🎥 Recherche de zones vidéo cliquables...")
        
        video_selectors = [
            'video',
            '.video-container',
            '.player-container',
            '.video-wrapper',
            '[class*="video"]',
            '[class*="player"]'
        ]
        
        for selector in video_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        print(f"   ✅ Zone vidéo trouvée: {selector}")
                        if self.smart_click(element):
                            return True
            except:
                continue
        
        print("   ❌ Bouton play non trouvé")
        return False
    
    def extract_video_urls_ultra(self):
        """Extraction ultra-avancée des URLs vidéo"""
        print("🎥 Extraction ultra-avancée des liens vidéo...")
        
        video_urls = []
        
        # Attendre le chargement de la vidéo
        time.sleep(8)
        
        try:
            # Méthode 1: Éléments video et source
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"   📹 Video src: {src}")
                
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append(src)
                        print(f"   📹 Source src: {src}")
            
            # Méthode 2: Analyse du code source avec regex ultra-précis
            page_source = self.driver.page_source
            
            ultra_patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm|mkv|avi)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm|mkv|avi)[^"]*)"',
                r'"url":\s*"([^"]+\.(?:mp4|m3u8|webm|mkv|avi)[^"]*)"',
                r'https?://[^"\s]+uqload[^"\s]*\.(?:mp4|m3u8|webm)',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?',
                r'"(https?://[^"]+/[^"]+\.(?:mp4|m3u8))"',
                r'src=["\']([^"\']+\.(?:mp4|m3u8|webm))["\']',
                r'file=["\']([^"\']+\.(?:mp4|m3u8|webm))["\']'
            ]
            
            for pattern in ultra_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"   📹 Regex: {match}")
            
            # Méthode 3: Interception des requêtes réseau
            try:
                network_urls = self.driver.execute_script("""
                    var urls = [];
                    
                    // Performance API
                    if (window.performance && window.performance.getEntriesByType) {
                        var entries = window.performance.getEntriesByType('resource');
                        for (var i = 0; i < entries.length; i++) {
                            var url = entries[i].name;
                            if (url.includes('.mp4') || url.includes('.m3u8') || 
                                url.includes('video') || url.includes('stream') ||
                                url.includes('uqload')) {
                                urls.push(url);
                            }
                        }
                    }
                    
                    // Chercher dans les variables globales
                    for (var prop in window) {
                        try {
                            var val = window[prop];
                            if (typeof val === 'string' && val.includes('http') && 
                                (val.includes('.mp4') || val.includes('.m3u8'))) {
                                urls.push(val);
                            }
                        } catch(e) {}
                    }
                    
                    return urls;
                """)
                
                for url in network_urls:
                    if url not in video_urls and url.startswith('http'):
                        video_urls.append(url)
                        print(f"   📹 Network: {url}")
                        
            except Exception as e:
                print(f"   ⚠️ Erreur network: {e}")
            
            # Méthode 4: Recherche dans les iframes
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframes:
                try:
                    self.driver.switch_to.frame(iframe)
                    
                    iframe_videos = self.driver.find_elements(By.TAG_NAME, "video")
                    for video in iframe_videos:
                        src = video.get_attribute('src')
                        if src and src.startswith('http') and src not in video_urls:
                            video_urls.append(src)
                            print(f"   📹 Iframe video: {src}")
                    
                    self.driver.switch_to.default_content()
                    
                except:
                    self.driver.switch_to.default_content()
                    continue
            
        except Exception as e:
            print(f"   ⚠️ Erreur extraction: {e}")
        
        # Nettoyer et valider les URLs
        clean_urls = []
        for url in video_urls:
            if url and url.startswith('http') and len(url) > 20:
                # Vérifier que c'est bien un lien vidéo
                if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.webm', 'video', 'stream']):
                    clean_urls.append(url)
        
        return list(set(clean_urls))  # Supprimer les doublons
    
    def run_full_automation(self, url):
        """Lance l'automatisation complète"""
        print("🚀 EXTRACTEUR UQLOAD 100% AUTOMATIQUE")
        print("=" * 60)
        print("🎯 Objectif: Extraction complètement automatique")
        print(f"🔗 URL: {url}")
        print()
        
        try:
            # Étape 1: Configuration du navigateur
            if not self.setup_super_stealth_driver():
                return None
            
            # Étape 2: Chargement de la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            
            # Étape 3: Attente Cloudflare
            self.smart_wait_cloudflare()
            
            # Étape 4: Recherche UQload
            uqload_button = self.find_uqload_with_ai()
            
            if not uqload_button:
                print("❌ UQload non trouvé - arrêt")
                return None
            
            # Étape 5: Clic sur UQload
            print("🖱️ Clic automatique sur UQload...")
            if not self.smart_click(uqload_button):
                print("❌ Impossible de cliquer sur UQload")
                return None
            
            print("✅ UQload cliqué avec succès!")
            time.sleep(5)
            
            # Étape 6: Recherche et clic sur play
            if self.find_and_click_play_ultra():
                print("✅ Bouton play cliqué automatiquement!")
                time.sleep(8)
            else:
                print("⚠️ Play non trouvé - extraction directe...")
            
            # Étape 7: Extraction des liens vidéo
            video_urls = self.extract_video_urls_ultra()
            
            if video_urls:
                result = {
                    'url': url,
                    'video_links': video_urls,
                    'primary_link': video_urls[0],
                    'extraction_method': 'full_automation',
                    'success': True,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_links_found': len(video_urls)
                }
                
                # Sauvegarder
                with open('full_auto_uqload_result.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("\n" + "="*60)
                print("🎉 EXTRACTION AUTOMATIQUE RÉUSSIE!")
                print("="*60)
                
                print(f"🎯 LIEN VIDÉO PRINCIPAL:")
                print(f"   {result['primary_link']}")
                
                if len(video_urls) > 1:
                    print(f"\n📹 TOUS LES LIENS TROUVÉS ({len(video_urls)}):")
                    for i, link in enumerate(video_urls, 1):
                        print(f"   {i}. {link}")
                
                print(f"\n💾 Résultats sauvegardés: full_auto_uqload_result.json")
                
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur fatale: {e}")
            return None
        
        finally:
            if self.driver:
                print("\n⏳ Fermeture automatique dans 10 secondes...")
                time.sleep(10)
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🤖 EXTRACTEUR UQLOAD 100% AUTOMATIQUE")
    print("=" * 60)
    print("✨ AUCUNE INTERVENTION MANUELLE REQUISE")
    print("🎯 Le script fait TOUT automatiquement:")
    print("   1. 🔍 Trouve le bouton UQload")
    print("   2. 🖱️ Clique sur UQload")
    print("   3. ▶️ Clique sur le bouton play")
    print("   4. 🎥 Extrait le lien vidéo")
    print()
    
    extractor = FullAutoUQloadExtractor(show_browser=True)
    result = extractor.run_full_automation(url)
    
    if result and result['success']:
        print(f"\n🏆 MISSION ACCOMPLIE!")
        print(f"🎬 Lien vidéo extrait automatiquement:")
        print(f"🔗 {result['primary_link']}")
    else:
        print(f"\n❌ Extraction automatique échouée")
        print(f"💡 Le site peut avoir une protection trop avancée")

if __name__ == "__main__":
    main()
