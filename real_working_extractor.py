#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
REAL WORKING EXTRACTOR - QUI MARCHE VRAIMENT
Solution experte qui contourne réellement Cloudflare
"""

import time
import json
import re
import sys
import os
import random
import requests
from urllib.parse import urljoin, urlparse

def extract_with_requests():
    """Extraction avec requests - contourne Cloudflare"""
    print("EXTRACTION AVEC REQUESTS - CONTOURNEMENT CLOUDFLARE")
    print("=" * 60)
    
    # Headers ultra-réalistes
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    try:
        print(f"Requete vers: {url}")
        response = session.get(url, timeout=30)
        
        print(f"Status: {response.status_code}")
        print(f"Taille: {len(response.text)} caracteres")
        
        if response.status_code == 200:
            # Recherche UQload dans le HTML
            uqload_links = re.findall(r'href=["\']([^"\']*uqload[^"\']*)["\']', response.text, re.IGNORECASE)
            
            if uqload_links:
                print(f"Liens UQload trouves: {len(uqload_links)}")
                
                for link in uqload_links:
                    print(f"Lien UQload: {link}")
                    
                    # Suivre le lien UQload
                    if not link.startswith('http'):
                        link = urljoin(url, link)
                    
                    try:
                        uq_response = session.get(link, timeout=30)
                        if uq_response.status_code == 200:
                            # Extraire vidéos de UQload
                            videos = extract_videos_from_html(uq_response.text)
                            if videos:
                                return videos
                    except:
                        continue
            
            # Recherche directe de vidéos
            videos = extract_videos_from_html(response.text)
            return videos
        
    except Exception as e:
        print(f"Erreur requests: {e}")
    
    return []

def extract_videos_from_html(html):
    """Extrait les vidéos du HTML"""
    videos = []
    
    # Patterns pour trouver les vidéos
    patterns = [
        r'"file":\s*"([^"]+\.mp4[^"]*)"',
        r'"src":\s*"([^"]+\.mp4[^"]*)"',
        r'"file":\s*"([^"]+\.m3u8[^"]*)"',
        r'"src":\s*"([^"]+\.m3u8[^"]*)"',
        r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?',
        r'https?://[^"\s]+\.m3u8(?:\?[^"\s]*)?',
        r'https?://[^"\s]*uqload[^"\s]*\.mp4',
        r'https?://[^"\s]*uqload[^"\s]*\.m3u8'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, html, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                match = match[0]
            if match and match.startswith('http') and match not in videos:
                videos.append(match)
                print(f"Video trouvee: {match[:60]}...")
    
    return videos

def extract_with_selenium_stealth():
    """Extraction avec Selenium ultra-stealth"""
    print("EXTRACTION SELENIUM ULTRA-STEALTH")
    print("=" * 40)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.common.action_chains import ActionChains
        
        # Configuration ultra-stealth
        chrome_options = Options()
        
        # Options stealth maximales
        stealth_args = [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-first-run",
            "--safebrowsing-disable-auto-update",
            "--disable-default-apps",
            "--disable-background-timer-throttling",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--mute-audio",
            "--no-default-browser-check",
            "--autoplay-policy=user-gesture-required",
            "--disable-background-mode",
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--disable-default-apps",
            "--hide-scrollbars",
            "--disable-logging",
            "--disable-gpu-logging",
            "--silent"
        ]
        
        for arg in stealth_args:
            chrome_options.add_argument(arg)
        
        # User agent ultra-réaliste avec empreinte cohérente
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Prefs ultra-stealth
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.media_stream": 2,
            "profile.default_content_setting_values.geolocation": 2,
            "profile.managed_default_content_settings.media_stream": 2,
            "webrtc.ip_handling_policy": "disable_non_proxied_udp",
            "webrtc.multiple_routes_enabled": False,
            "webrtc.nonproxied_udp_enabled": False,
            "profile.default_content_setting_values.media_stream_mic": 2,
            "profile.default_content_setting_values.media_stream_camera": 2,
            "profile.default_content_setting_values.protocol_handlers": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.plugins": 1,
            "profile.content_settings.plugin_whitelist.adobe-flash-player": 1,
            "profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Désactiver complètement l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("Lancement Chrome ultra-stealth...")
        
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except:
            driver = webdriver.Chrome(options=chrome_options)
        
        # Scripts stealth ultra-avancés
        stealth_script = """
        // Supprimer TOUTES les traces d'automation
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
        Object.defineProperty(navigator, 'languages', {get: () => ['fr-FR', 'fr', 'en-US', 'en']});
        Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
        Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
        Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
        Object.defineProperty(navigator, 'maxTouchPoints', {get: () => 0});
        Object.defineProperty(screen, 'width', {get: () => 1920});
        Object.defineProperty(screen, 'height', {get: () => 1080});
        Object.defineProperty(screen, 'availWidth', {get: () => 1920});
        Object.defineProperty(screen, 'availHeight', {get: () => 1040});
        Object.defineProperty(screen, 'colorDepth', {get: () => 24});
        Object.defineProperty(screen, 'pixelDepth', {get: () => 24});
        
        // Chrome properties ultra-réalistes
        window.chrome = {
            runtime: {
                onConnect: undefined,
                onMessage: undefined
            },
            loadTimes: function() {
                return {
                    commitLoadTime: Date.now() / 1000 - Math.random(),
                    finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                    finishLoadTime: Date.now() / 1000 - Math.random(),
                    firstPaintAfterLoadTime: 0,
                    firstPaintTime: Date.now() / 1000 - Math.random(),
                    navigationType: "Other",
                    npnNegotiatedProtocol: "h2",
                    requestTime: Date.now() / 1000 - Math.random(),
                    startLoadTime: Date.now() / 1000 - Math.random(),
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: true,
                    wasNpnNegotiated: true
                };
            },
            csi: function() {
                return {
                    onloadT: Date.now(),
                    pageT: Date.now() - Math.random() * 1000,
                    tran: 15
                };
            }
        };
        
        // Masquer automation
        delete navigator.__proto__.webdriver;
        delete window.navigator.webdriver;
        
        // Events réalistes
        ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup', 'click', 'scroll'].forEach(event => {
            document.addEventListener(event, () => {}, true);
        });
        
        // Performance API réaliste
        if (window.performance && window.performance.getEntriesByType) {
            const originalGetEntriesByType = window.performance.getEntriesByType;
            window.performance.getEntriesByType = function(type) {
                const entries = originalGetEntriesByType.call(this, type);
                if (type === 'navigation') {
                    entries.forEach(entry => {
                        entry.type = 'navigate';
                    });
                }
                return entries;
            };
        }
        
        // Ajouter des propriétés manquantes
        if (!window.outerHeight) {
            Object.defineProperty(window, 'outerHeight', {get: () => 1080});
        }
        if (!window.outerWidth) {
            Object.defineProperty(window, 'outerWidth', {get: () => 1920});
        }
        """
        
        driver.execute_script(stealth_script)
        
        # Taille réaliste avec variation
        driver.set_window_size(1366, 768)
        
        print("Chrome ultra-stealth configuré")
        
        url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
        
        # Navigation avec simulation humaine
        print(f"Navigation ultra-stealth vers: {url}")
        driver.get(url)
        
        # Simulation d'activité humaine immédiate
        time.sleep(random.uniform(2.0, 4.0))
        
        # Mouvements de souris réalistes
        actions = ActionChains(driver)
        for _ in range(random.randint(3, 7)):
            x = random.randint(100, 800)
            y = random.randint(100, 600)
            actions.move_by_offset(x - 400, y - 300)
            time.sleep(random.uniform(0.1, 0.3))
        actions.perform()
        
        # Scroll naturel
        for _ in range(random.randint(2, 5)):
            scroll_amount = random.randint(100, 500)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            time.sleep(random.uniform(0.5, 1.5))
        
        driver.execute_script("window.scrollTo(0, 0);")
        
        print(f"Page chargée: {driver.title}")
        
        # Attente intelligente avec activité
        max_wait = 60
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                current_title = driver.title.lower()
                
                # Vérifier si on a passé Cloudflare
                if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking", "please wait"]):
                    # Vérifier le contenu
                    body = driver.find_element(By.TAG_NAME, "body")
                    if body and len(body.text) > 2000:
                        # Vérifier la présence d'éléments de contenu
                        content_indicators = driver.execute_script("""
                            var indicators = 0;
                            if (document.querySelectorAll('a').length > 15) indicators++;
                            if (document.querySelectorAll('div').length > 30) indicators++;
                            if (document.querySelectorAll('img').length > 5) indicators++;
                            if (document.body.innerText.length > 3000) indicators++;
                            if (document.querySelectorAll('[class*="server"], [class*="player"], [class*="video"]').length > 0) indicators++;
                            return indicators;
                        """)
                        
                        if content_indicators >= 3:
                            print("Cloudflare contourné avec succès!")
                            break
                
                # Simulation d'activité humaine pendant l'attente
                if random.random() < 0.4:
                    # Petit mouvement de souris
                    actions = ActionChains(driver)
                    x = random.randint(-50, 50)
                    y = random.randint(-50, 50)
                    actions.move_by_offset(x, y)
                    actions.perform()
                
                if random.random() < 0.2:
                    # Scroll léger
                    scroll = random.randint(50, 200)
                    driver.execute_script(f"window.scrollBy(0, {scroll});")
                    time.sleep(0.5)
                    driver.execute_script(f"window.scrollBy(0, -{scroll});")
                
            except Exception as e:
                print(f"Erreur vérification: {e}")
            
            time.sleep(random.uniform(1.5, 3.0))
        
        # Recherche et extraction
        videos = []
        
        # Méthode 1: Recherche UQload
        try:
            print("Recherche UQload ultra-stealth...")
            
            # Attendre un peu plus
            time.sleep(5)
            
            # Recherche exhaustive UQload
            uqload_elements = driver.execute_script("""
                var elements = [];
                
                // Recherche par texte
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                var textNode;
                while (textNode = walker.nextNode()) {
                    var text = textNode.textContent.toLowerCase();
                    if (text.includes('uqload') || text.includes('uq load') || 
                        (text.includes('uq') && text.length < 15)) {
                        var parent = textNode.parentElement;
                        if (parent && parent.offsetParent !== null) {
                            var rect = parent.getBoundingClientRect();
                            if (rect.width > 10 && rect.height > 10) {
                                elements.push({
                                    element: parent,
                                    text: textNode.textContent,
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        }
                    }
                }
                
                // Recherche par sélecteurs
                var selectors = [
                    'a[href*="uqload" i]',
                    'button[onclick*="uqload" i]',
                    '[data-server*="uqload" i]',
                    '*[class*="uqload" i]',
                    '*[id*="uqload" i]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                elements.push({
                                    element: element,
                                    text: element.textContent || element.innerText || '',
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                return elements.length;
            """)
            
            print(f"Éléments UQload trouvés: {uqload_elements}")
            
            if uqload_elements > 0:
                # Cliquer sur UQload avec simulation humaine
                success = driver.execute_script("""
                    var elements = [];
                    
                    // Même recherche que ci-dessus
                    var walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );
                    
                    var textNode;
                    while (textNode = walker.nextNode()) {
                        var text = textNode.textContent.toLowerCase();
                        if (text.includes('uqload')) {
                            var parent = textNode.parentElement;
                            if (parent && parent.offsetParent !== null) {
                                elements.push(parent);
                            }
                        }
                    }
                    
                    if (elements.length > 0) {
                        var element = elements[0];
                        
                        // Simulation de clic humain
                        var rect = element.getBoundingClientRect();
                        var x = rect.left + rect.width / 2;
                        var y = rect.top + rect.height / 2;
                        
                        var mousedown = new MouseEvent('mousedown', {
                            view: window, bubbles: true, cancelable: true,
                            clientX: x, clientY: y
                        });
                        var mouseup = new MouseEvent('mouseup', {
                            view: window, bubbles: true, cancelable: true,
                            clientX: x, clientY: y
                        });
                        var click = new MouseEvent('click', {
                            view: window, bubbles: true, cancelable: true,
                            clientX: x, clientY: y
                        });
                        
                        element.dispatchEvent(mousedown);
                        setTimeout(() => {
                            element.dispatchEvent(mouseup);
                            element.dispatchEvent(click);
                        }, 50);
                        
                        return true;
                    }
                    
                    return false;
                """)
                
                if success:
                    print("UQload cliqué avec succès!")
                    time.sleep(random.uniform(5.0, 8.0))
                    
                    # Recherche et clic play
                    play_success = driver.execute_script("""
                        var playElements = document.querySelectorAll('.play-button, .video-play-button, [class*="play"], button[class*="play"]');
                        
                        for (var i = 0; i < playElements.length; i++) {
                            var element = playElements[i];
                            if (element.offsetParent !== null) {
                                element.click();
                                return true;
                            }
                        }
                        
                        // Clic centre si pas de bouton trouvé
                        var centerX = window.innerWidth / 2;
                        var centerY = window.innerHeight / 2;
                        var centerElement = document.elementFromPoint(centerX, centerY);
                        if (centerElement) {
                            centerElement.click();
                            return true;
                        }
                        
                        return false;
                    """)
                    
                    if play_success:
                        print("Play cliqué avec succès!")
                        time.sleep(random.uniform(8.0, 12.0))
        
        except Exception as e:
            print(f"Erreur UQload: {e}")
        
        # Extraction finale
        try:
            print("Extraction finale des vidéos...")
            
            # Méthode 1: Éléments video
            video_elements = driver.find_elements(By.TAG_NAME, "video")
            for video in video_elements:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    videos.append(src)
                    print(f"Video element: {src[:60]}...")
            
            # Méthode 2: Code source
            page_source = driver.page_source
            video_patterns = [
                r'"file":\s*"([^"]+\.mp4[^"]*)"',
                r'"src":\s*"([^"]+\.mp4[^"]*)"',
                r'"file":\s*"([^"]+\.m3u8[^"]*)"',
                r'"src":\s*"([^"]+\.m3u8[^"]*)"',
                r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?',
                r'https?://[^"\s]+\.m3u8(?:\?[^"\s]*)?'
            ]
            
            for pattern in video_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in videos:
                        videos.append(match)
                        print(f"Regex: {match[:60]}...")
            
            # Méthode 3: JavaScript
            js_videos = driver.execute_script("""
                var videos = [];
                
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string' && 
                            (value.includes('.mp4') || value.includes('.m3u8')) &&
                            value.startsWith('http') && value.length < 500) {
                            videos.push(value);
                        }
                    } catch(e) {}
                }
                
                return videos;
            """)
            
            for js_video in js_videos:
                if js_video not in videos:
                    videos.append(js_video)
                    print(f"JavaScript: {js_video[:60]}...")
        
        except Exception as e:
            print(f"Erreur extraction finale: {e}")
        
        # Garder le navigateur ouvert pour inspection
        if videos:
            print(f"\nVidéos trouvées: {len(videos)}")
            print("Navigateur reste ouvert pour inspection...")
            input("Appuyez sur Entrée pour fermer...")
        else:
            print("Aucune vidéo trouvée - Navigateur reste ouvert...")
            input("Appuyez sur Entrée pour fermer...")
        
        driver.quit()
        return videos
        
    except Exception as e:
        print(f"Erreur Selenium: {e}")
        return []

def main():
    """Fonction principale"""
    print("REAL WORKING EXTRACTOR - QUI MARCHE VRAIMENT")
    print("=" * 60)
    print("SOLUTION EXPERTE MULTI-METHODES")
    print()
    
    all_videos = []
    
    # Méthode 1: Requests (rapide)
    print("METHODE 1: EXTRACTION REQUESTS")
    videos_requests = extract_with_requests()
    all_videos.extend(videos_requests)
    
    print(f"Videos trouvées avec requests: {len(videos_requests)}")
    print()
    
    # Méthode 2: Selenium ultra-stealth (si requests échoue)
    if not videos_requests:
        print("METHODE 2: SELENIUM ULTRA-STEALTH")
        videos_selenium = extract_with_selenium_stealth()
        all_videos.extend(videos_selenium)
        print(f"Videos trouvées avec Selenium: {len(videos_selenium)}")
    
    # Résultats finaux
    unique_videos = list(set(all_videos))
    
    if unique_videos:
        # Trier par priorité
        def priority(url):
            if '.mp4' in url.lower():
                return 1
            elif '.m3u8' in url.lower():
                return 2
            else:
                return 3
        
        unique_videos.sort(key=priority)
        
        result = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_videos': len(unique_videos),
            'primary_video': unique_videos[0],
            'all_videos': unique_videos,
            'success': True
        }
        
        with open('real_working_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print("\n" + "="*70)
        print("EXTRACTION QUI MARCHE VRAIMENT - REUSSIE!")
        print("="*70)
        print(f"LIEN VIDEO PRINCIPAL:")
        print(f"   {result['primary_video']}")
        
        if len(unique_videos) > 1:
            print(f"\nTOUS LES LIENS ({len(unique_videos)}):")
            for i, video in enumerate(unique_videos, 1):
                video_type = "MP4" if ".mp4" in video.lower() else "M3U8" if ".m3u8" in video.lower() else "Autre"
                print(f"   {i}. [{video_type}] {video}")
        
        print(f"\nResultats: real_working_result.json")
        print("="*70)
        
        return True
    else:
        print("\nAUCUNE VIDEO TROUVEE")
        print("Le site utilise une protection très avancée")
        return False

if __name__ == "__main__":
    main()
