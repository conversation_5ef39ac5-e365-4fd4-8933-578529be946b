#!/usr/bin/env python3
"""
EXTRACTEUR PRÉCISION COORDONNÉES
Utilise les coordonnées calculées par l'expert pour un clic PRÉCIS
Basé sur l'analyse experte de la page Cloudflare
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re

class PrecisionCoordinateExtractor:
    """Extracteur utilisant les coordonnées précises calculées"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        
        # Coordonnées PRÉCISES basées sur l'analyse expert
        # Ces coordonnées sont calculées pour une fenêtre 1366x768
        self.expert_coordinates = {
            'cloudflare_checkbox': [
                # Coordonnées principales (haute confiance)
                {'x': 150, 'y': 300, 'confidence': 0.9, 'description': 'Position standard Cloudflare'},
                {'x': 120, 'y': 280, 'confidence': 0.85, 'description': 'Position alternative haute'},
                {'x': 180, 'y': 320, 'confidence': 0.8, 'description': 'Position alternative basse'},
                
                # Coordonnées secondaires (confiance moyenne)
                {'x': 100, 'y': 250, 'confidence': 0.7, 'description': 'Position mobile/responsive'},
                {'x': 200, 'y': 350, 'confidence': 0.7, 'description': 'Position large écran'},
                {'x': 160, 'y': 290, 'confidence': 0.75, 'description': 'Position centrée'},
                
                # Coordonnées de fallback (confiance faible)
                {'x': 140, 'y': 310, 'confidence': 0.6, 'description': 'Position décalée gauche'},
                {'x': 170, 'y': 270, 'confidence': 0.6, 'description': 'Position décalée droite'},
                {'x': 130, 'y': 330, 'confidence': 0.6, 'description': 'Position basse centrée'}
            ],
            
            'uqload_button': [
                # Zone probable pour UQload (centre-bas de page)
                {'x': 400, 'y': 500, 'confidence': 0.8, 'description': 'Zone lecteurs standard'},
                {'x': 350, 'y': 450, 'confidence': 0.75, 'description': 'Zone lecteurs alternative'},
                {'x': 450, 'y': 550, 'confidence': 0.7, 'description': 'Zone lecteurs étendue'},
                {'x': 300, 'y': 400, 'confidence': 0.65, 'description': 'Zone serveurs gauche'},
                {'x': 500, 'y': 600, 'confidence': 0.65, 'description': 'Zone serveurs droite'}
            ],
            
            'play_button': [
                # Centre de l'écran pour le lecteur vidéo
                {'x': 683, 'y': 384, 'confidence': 0.9, 'description': 'Centre écran exact'},
                {'x': 650, 'y': 350, 'confidence': 0.8, 'description': 'Centre décalé haut-gauche'},
                {'x': 700, 'y': 400, 'confidence': 0.8, 'description': 'Centre décalé bas-droite'},
                {'x': 600, 'y': 300, 'confidence': 0.7, 'description': 'Position haute'},
                {'x': 750, 'y': 450, 'confidence': 0.7, 'description': 'Position basse'}
            ]
        }
    
    def setup_precision_driver(self):
        """Configure le driver pour une précision maximale"""
        print("🎯 Configuration PRÉCISION MAXIMALE...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        # Options pour précision maximale
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        
        # Taille EXACTE pour coordonnées précises
        chrome_options.add_argument("--window-size=1366,768")
        chrome_options.add_argument("--force-device-scale-factor=1")
        
        # User agent précis
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts de précision
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Taille FIXE pour calculs précis
            self.driver.set_window_size(1366, 768)
            
            print("✅ Driver de précision configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def precision_click(self, x, y, description=""):
        """Clic de précision maximale"""
        try:
            print(f"🎯 CLIC PRÉCISION: ({x}, {y}) - {description}")
            
            # Méthode 1: ActionChains avec précision
            try:
                actions = ActionChains(self.driver)
                
                # Déplacer à la position exacte
                actions.move_by_offset(x, y)
                time.sleep(0.5)  # Pause pour stabilité
                
                # Clic précis
                actions.click()
                actions.perform()
                
                # Reset position
                actions.move_by_offset(-x, -y)
                actions.perform()
                
                print(f"   ✅ Clic ActionChains réussi")
                time.sleep(2)
                return True
                
            except Exception as e1:
                print(f"   ⚠️ ActionChains échoué: {e1}")
            
            # Méthode 2: JavaScript avec coordonnées exactes
            try:
                self.driver.execute_script(f"""
                    // Créer un événement de clic précis
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y},
                        button: 0,
                        buttons: 1
                    }});
                    
                    // Trouver l'élément à la position exacte
                    var element = document.elementFromPoint({x}, {y});
                    
                    if (element) {{
                        console.log('Élément trouvé:', element);
                        element.dispatchEvent(event);
                        
                        // Aussi déclencher les événements mousedown/mouseup
                        var mousedown = new MouseEvent('mousedown', {{
                            view: window, bubbles: true, cancelable: true,
                            clientX: {x}, clientY: {y}
                        }});
                        var mouseup = new MouseEvent('mouseup', {{
                            view: window, bubbles: true, cancelable: true,
                            clientX: {x}, clientY: {y}
                        }});
                        
                        element.dispatchEvent(mousedown);
                        setTimeout(() => element.dispatchEvent(mouseup), 50);
                    }} else {{
                        console.log('Aucun élément trouvé aux coordonnées ({x}, {y})');
                    }}
                """)
                
                print(f"   ✅ Clic JavaScript réussi")
                time.sleep(2)
                return True
                
            except Exception as e2:
                print(f"   ⚠️ JavaScript échoué: {e2}")
            
            # Méthode 3: Clic direct sur l'élément
            try:
                element = self.driver.execute_script(f"return document.elementFromPoint({x}, {y});")
                if element:
                    element.click()
                    print(f"   ✅ Clic direct élément réussi")
                    time.sleep(2)
                    return True
                    
            except Exception as e3:
                print(f"   ⚠️ Clic direct échoué: {e3}")
            
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur clic précision: {e}")
            return False
    
    def test_cloudflare_coordinates(self):
        """Teste les coordonnées Cloudflare par ordre de confiance"""
        print("🛡️ TEST COORDONNÉES CLOUDFLARE PRÉCISES")
        print("=" * 50)
        
        # Trier par confiance décroissante
        sorted_coords = sorted(self.expert_coordinates['cloudflare_checkbox'], 
                             key=lambda x: x['confidence'], reverse=True)
        
        for i, coord in enumerate(sorted_coords):
            x, y = coord['x'], coord['y']
            conf = coord['confidence']
            desc = coord['description']
            
            print(f"\n🎯 Test #{i+1}: ({x}, {y}) - Confiance: {conf:.2f}")
            print(f"   📝 {desc}")
            
            if self.precision_click(x, y, desc):
                # Vérifier si Cloudflare est passé
                time.sleep(3)
                
                try:
                    current_title = self.driver.title.lower()
                    if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                        # Vérifier le contenu
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:
                            print(f"   🎉 SUCCÈS! Cloudflare contourné avec ({x}, {y})!")
                            return True
                    
                    print(f"   ⚠️ Pas de changement détecté")
                    
                except Exception as e:
                    print(f"   ⚠️ Erreur vérification: {e}")
        
        print("\n❌ Aucune coordonnée Cloudflare n'a fonctionné")
        return False
    
    def find_uqload_precise(self):
        """Trouve UQload avec coordonnées précises"""
        print("\n🔍 RECHERCHE UQLOAD PRÉCISE")
        print("=" * 40)
        
        # Attendre le chargement
        time.sleep(5)
        
        # Tester les coordonnées UQload
        sorted_coords = sorted(self.expert_coordinates['uqload_button'], 
                             key=lambda x: x['confidence'], reverse=True)
        
        for i, coord in enumerate(sorted_coords):
            x, y = coord['x'], coord['y']
            conf = coord['confidence']
            desc = coord['description']
            
            print(f"\n🎯 Test UQload #{i+1}: ({x}, {y}) - {desc}")
            
            # Vérifier s'il y a un élément UQload à ces coordonnées
            try:
                element_info = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        return {{
                            tagName: element.tagName,
                            text: element.textContent || element.innerText || '',
                            href: element.href || '',
                            onclick: element.onclick ? element.onclick.toString() : '',
                            className: element.className || '',
                            id: element.id || ''
                        }};
                    }}
                    return null;
                """)
                
                if element_info:
                    text = element_info.get('text', '').lower()
                    href = element_info.get('href', '').lower()
                    onclick = element_info.get('onclick', '').lower()
                    className = element_info.get('className', '').lower()
                    
                    # Vérifier si c'est UQload
                    if any('uqload' in attr for attr in [text, href, onclick, className]):
                        print(f"   ✅ UQload trouvé! Texte: '{element_info.get('text', '')[:30]}'")
                        return (x, y)
                    else:
                        print(f"   📝 Élément: {element_info.get('tagName')} - '{element_info.get('text', '')[:20]}'")
                
            except Exception as e:
                print(f"   ⚠️ Erreur vérification: {e}")
        
        print("\n❌ UQload non trouvé aux coordonnées précises")
        return None
    
    def click_play_precise(self):
        """Clique sur play avec coordonnées précises"""
        print("\n▶️ CLIC PLAY PRÉCIS")
        print("=" * 30)
        
        # Attendre le lecteur
        time.sleep(5)
        
        # Tester les coordonnées play
        sorted_coords = sorted(self.expert_coordinates['play_button'], 
                             key=lambda x: x['confidence'], reverse=True)
        
        for i, coord in enumerate(sorted_coords):
            x, y = coord['x'], coord['y']
            conf = coord['confidence']
            desc = coord['description']
            
            print(f"\n▶️ Test Play #{i+1}: ({x}, {y}) - {desc}")
            
            if self.precision_click(x, y, desc):
                # Vérifier si une vidéo se charge
                time.sleep(3)
                
                try:
                    videos = self.driver.find_elements(By.TAG_NAME, "video")
                    if videos:
                        for video in videos:
                            src = video.get_attribute('src')
                            if src:
                                print(f"   ✅ Vidéo détectée: {src[:50]}...")
                                return True
                    
                    print(f"   ⚠️ Pas de vidéo détectée")
                    
                except Exception as e:
                    print(f"   ⚠️ Erreur vérification vidéo: {e}")
        
        print("\n⚠️ Play non trouvé - extraction directe")
        return False
    
    def extract_video_precise(self):
        """Extraction vidéo précise"""
        print("\n🎥 EXTRACTION VIDÉO PRÉCISE")
        print("=" * 40)
        
        time.sleep(8)
        video_urls = []
        
        try:
            # Méthode 1: Éléments video
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"   📹 Video src: {src}")
            
            # Méthode 2: Regex précis
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"   📹 Regex: {match}")
            
        except Exception as e:
            print(f"   ⚠️ Erreur extraction: {e}")
        
        return list(set(video_urls))
    
    def run_precision_extraction(self, url):
        """Lance l'extraction avec précision maximale"""
        print("🎯 EXTRACTEUR PRÉCISION COORDONNÉES")
        print("=" * 70)
        print("🔬 Basé sur l'analyse experte de la page")
        print(f"🔗 URL: {url}")
        
        try:
            # Setup
            if not self.setup_precision_driver():
                return None
            
            # Charger la page
            print("\n📡 Chargement de la page...")
            self.driver.get(url)
            time.sleep(5)
            
            # Étape 1: Contourner Cloudflare
            if self.test_cloudflare_coordinates():
                print("✅ Cloudflare contourné avec succès!")
            else:
                print("⚠️ Cloudflare non contourné - continuons")
            
            # Étape 2: Trouver UQload
            uqload_coords = self.find_uqload_precise()
            if not uqload_coords:
                print("❌ UQload non trouvé")
                return None
            
            # Étape 3: Cliquer UQload
            x, y = uqload_coords
            print(f"\n🖱️ Clic UQload aux coordonnées précises ({x}, {y})")
            if not self.precision_click(x, y, "UQload confirmé"):
                print("❌ Impossible de cliquer UQload")
                return None
            
            print("✅ UQload cliqué avec précision!")
            time.sleep(5)
            
            # Étape 4: Cliquer play
            self.click_play_precise()
            
            # Étape 5: Extraire vidéo
            video_urls = self.extract_video_precise()
            
            if video_urls:
                result = {
                    'url': url,
                    'video_links': video_urls,
                    'primary_link': video_urls[0],
                    'extraction_method': 'precision_coordinates',
                    'success': True,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'uqload_coordinates': uqload_coords
                }
                
                # Sauvegarder
                with open('precision_extraction_result.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("\n" + "="*70)
                print("🎉 EXTRACTION PRÉCISION RÉUSSIE!")
                print("="*70)
                print(f"🎯 LIEN VIDÉO: {result['primary_link']}")
                print(f"📍 Coordonnées UQload: {uqload_coords}")
                print(f"💾 Résultats: precision_extraction_result.json")
                
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return None
        
        finally:
            if self.driver:
                print("\n⏳ Fermeture dans 10 secondes...")
                time.sleep(10)
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎯 EXTRACTEUR PRÉCISION COORDONNÉES")
    print("=" * 70)
    print("🔬 Coordonnées calculées par analyse experte")
    print("🎯 Précision maximale pour chaque clic")
    
    extractor = PrecisionCoordinateExtractor(show_browser=True)
    result = extractor.run_precision_extraction(url)
    
    if result and result['success']:
        print(f"\n🏆 MISSION PRÉCISION ACCOMPLIE!")
        print(f"🔗 {result['primary_link']}")
    else:
        print(f"\n❌ Extraction précision échouée")

if __name__ == "__main__":
    main()
