#!/usr/bin/env python3
"""
Automatisation du clic sur la case à cocher Cloudflare
Détecte et clique automatiquement sur "Je ne suis pas un robot"
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import random

class CloudflareCheckboxClicker:
    """Automatise le clic sur la case à cocher Cloudflare"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        
    def setup_human_like_driver(self):
        """Configure un driver qui imite un humain"""
        print("🤖 Configuration du navigateur humain-like...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        # Options pour imiter un humain
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        
        # User agent très réaliste
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Désactiver l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Prefs pour ressembler à un vrai navigateur
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts anti-détection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr']})")
            
            # Taille de fenêtre réaliste
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def find_cloudflare_checkbox(self):
        """Trouve la case à cocher Cloudflare"""
        print("🔍 Recherche de la case à cocher Cloudflare...")
        
        # Sélecteurs pour la case à cocher Cloudflare
        checkbox_selectors = [
            # Cloudflare standard
            'input[type="checkbox"]',
            '.cf-turnstile input',
            '#cf-turnstile input',
            '[data-sitekey] input',
            
            # reCAPTCHA (au cas où)
            '.recaptcha-checkbox',
            '#recaptcha-anchor',
            '.recaptcha-checkbox-border',
            
            # hCaptcha
            '.h-captcha iframe',
            '#h-captcha-response',
            
            # Cases à cocher génériques
            'input[type="checkbox"][id*="captcha"]',
            'input[type="checkbox"][class*="captcha"]',
            'input[type="checkbox"][name*="captcha"]',
            
            # Cloudflare Turnstile
            '.cf-turnstile',
            '[data-theme] input[type="checkbox"]',
            
            # Autres variantes
            'label[for*="captcha"] input',
            '.challenge-form input[type="checkbox"]'
        ]
        
        for selector in checkbox_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ Case à cocher trouvée: {selector}")
                        return element
            except Exception as e:
                continue
        
        # Recherche dans les iframes
        print("   🔍 Recherche dans les iframes...")
        
        iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
        for i, iframe in enumerate(iframes):
            try:
                print(f"   📺 Vérification iframe {i+1}/{len(iframes)}")
                
                # Vérifier si l'iframe contient du contenu Cloudflare/CAPTCHA
                iframe_src = iframe.get_attribute('src') or ''
                iframe_title = iframe.get_attribute('title') or ''
                
                if any(keyword in iframe_src.lower() for keyword in ['cloudflare', 'captcha', 'turnstile', 'challenge']):
                    print(f"   🎯 Iframe Cloudflare détectée: {iframe_src}")
                    
                    self.driver.switch_to.frame(iframe)
                    
                    for selector in checkbox_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                if element.is_displayed():
                                    print(f"   ✅ Case trouvée dans iframe: {selector}")
                                    return element, iframe  # Retourner l'élément et l'iframe
                        except:
                            continue
                    
                    self.driver.switch_to.default_content()
                
            except Exception as e:
                self.driver.switch_to.default_content()
                continue
        
        print("   ❌ Case à cocher non trouvée")
        return None
    
    def human_like_click(self, element):
        """Effectue un clic qui imite un humain"""
        try:
            print("🖱️ Clic humain-like sur la case à cocher...")
            
            # Scroll vers l'élément avec mouvement naturel
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", element)
            
            # Attendre un délai humain
            time.sleep(random.uniform(1.5, 3.0))
            
            # Mouvement de souris naturel vers l'élément
            actions = ActionChains(self.driver)
            
            # Déplacer la souris vers l'élément avec une trajectoire naturelle
            actions.move_to_element(element)
            time.sleep(random.uniform(0.5, 1.0))
            
            # Pause avant le clic (comme un humain)
            time.sleep(random.uniform(0.2, 0.5))
            
            # Clic avec un petit délai
            actions.click(element)
            actions.perform()
            
            print("   ✅ Clic effectué!")
            
            # Attendre un peu après le clic
            time.sleep(random.uniform(2.0, 4.0))
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur clic: {e}")
            
            # Essayer avec JavaScript en dernier recours
            try:
                print("   🔄 Tentative avec JavaScript...")
                self.driver.execute_script("arguments[0].click();", element)
                time.sleep(random.uniform(2.0, 4.0))
                return True
            except:
                return False
    
    def wait_for_verification(self, max_wait=30):
        """Attend que la vérification soit terminée"""
        print("⏳ Attente de la vérification...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                # Vérifier si on est toujours sur une page de vérification
                current_title = self.driver.title.lower()
                current_url = self.driver.current_url.lower()
                
                # Indicateurs que la vérification est terminée
                if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                    # Vérifier si on a accès au contenu du site
                    try:
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:  # Page avec contenu
                            print("   ✅ Vérification réussie - accès au site!")
                            return True
                    except:
                        pass
                
                # Vérifier s'il y a une nouvelle case à cocher ou un CAPTCHA
                checkbox = self.find_cloudflare_checkbox()
                if checkbox:
                    print("   🔄 Nouvelle vérification détectée...")
                    if isinstance(checkbox, tuple):
                        element, iframe = checkbox
                        self.driver.switch_to.frame(iframe)
                        self.human_like_click(element)
                        self.driver.switch_to.default_content()
                    else:
                        self.human_like_click(checkbox)
                
                time.sleep(2)
                
            except Exception as e:
                time.sleep(1)
                continue
        
        print("   ⚠️ Timeout de vérification")
        return False
    
    def bypass_cloudflare_with_checkbox(self, url):
        """Contourne Cloudflare en cliquant sur la case à cocher"""
        print("🛡️ CONTOURNEMENT CLOUDFLARE AVEC CASE À COCHER")
        print("=" * 60)
        print(f"🔗 URL: {url}")
        
        try:
            if not self.setup_human_like_driver():
                return False
            
            # Charger la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            
            # Attendre le chargement initial
            time.sleep(5)
            
            # Boucle de vérification Cloudflare
            max_attempts = 3
            for attempt in range(max_attempts):
                print(f"\n🔄 Tentative {attempt + 1}/{max_attempts}")
                
                # Chercher la case à cocher
                checkbox_result = self.find_cloudflare_checkbox()
                
                if checkbox_result:
                    if isinstance(checkbox_result, tuple):
                        # Case dans un iframe
                        element, iframe = checkbox_result
                        print("   📺 Case dans iframe détectée")
                        self.driver.switch_to.frame(iframe)
                        success = self.human_like_click(element)
                        self.driver.switch_to.default_content()
                    else:
                        # Case directe
                        success = self.human_like_click(checkbox_result)
                    
                    if success:
                        # Attendre la vérification
                        if self.wait_for_verification():
                            print("\n🎉 CLOUDFLARE CONTOURNÉ AVEC SUCCÈS!")
                            return True
                    
                else:
                    # Pas de case à cocher trouvée, vérifier si on a déjà accès
                    try:
                        current_title = self.driver.title.lower()
                        if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare"]):
                            body = self.driver.find_element(By.TAG_NAME, "body")
                            if body and len(body.text) > 1000:
                                print("✅ Accès direct au site - pas de Cloudflare!")
                                return True
                    except:
                        pass
                    
                    print("   ❌ Aucune case à cocher trouvée")
                
                # Attendre avant la prochaine tentative
                if attempt < max_attempts - 1:
                    time.sleep(5)
            
            print("\n❌ Impossible de contourner Cloudflare")
            return False
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
        
        finally:
            # Ne pas fermer le navigateur pour permettre l'inspection
            if self.driver:
                print("\n⏳ Navigateur reste ouvert pour inspection...")
                input("Appuyez sur Entrée pour fermer le navigateur...")
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🛡️ CONTOURNEUR CLOUDFLARE - CASE À COCHER")
    print("=" * 60)
    print("🎯 Objectif: Cliquer automatiquement sur la case à cocher")
    print("🤖 Méthode: Simulation de comportement humain")
    print()
    
    clicker = CloudflareCheckboxClicker(show_browser=True)
    
    if clicker.bypass_cloudflare_with_checkbox(url):
        print("\n🏆 SUCCÈS! Cloudflare contourné!")
        print("🎬 Vous pouvez maintenant utiliser les autres extracteurs!")
        
        # Optionnel: Lancer l'extracteur UQload après le contournement
        choice = input("\nVoulez-vous lancer l'extracteur UQload maintenant? (o/n): ")
        if choice.lower().strip() in ['o', 'oui', 'y', 'yes']:
            print("🚀 Lancement de l'extracteur UQload...")
            # Ici on pourrait lancer l'extracteur UQload
    else:
        print("\n❌ Échec du contournement")
        print("💡 Suggestions:")
        print("   • Essayez avec un VPN différent")
        print("   • Attendez quelques minutes et réessayez")
        print("   • Utilisez le guide manuel")

if __name__ == "__main__":
    main()
