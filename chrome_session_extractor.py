#!/usr/bin/env python3
"""
CHROME SESSION EXTRACTOR - UTILISE VOTRE CHROME OUVERT
Expert en: Connexion session existante, Remote debugging, Automation sur session active
OBJECTIF: Se connecter à votre Chrome ouvert et extraire automatiquement
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
import time
import json
import re
import subprocess
import psutil
import requests
import random

class ChromeSessionExtractor:
    """Extracteur qui utilise votre session Chrome existante"""
    
    def __init__(self):
        self.driver = None
        self.debug_port = None
        
    def find_existing_chrome_debug_port(self):
        """Trouve le port de debug du Chrome ouvert ou l'active"""
        print("🔍 Recherche de votre session Chrome...")
        
        # Méthode 1: Chercher un port debug existant
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        for arg in cmdline:
                            if '--remote-debugging-port=' in str(arg):
                                port = str(arg).split('=')[1]
                                print(f"   ✅ Port debug existant trouvé: {port}")
                                return int(port)
            except:
                continue
        
        # Méthode 2: Tester les ports standards
        standard_ports = [9222, 9223, 9224, 9225, 9226]
        for port in standard_ports:
            try:
                response = requests.get(f'http://localhost:{port}/json', timeout=2)
                if response.status_code == 200:
                    print(f"   ✅ Port debug actif: {port}")
                    return port
            except:
                continue
        
        # Méthode 3: Activer le debugging sur Chrome existant
        print("   🔧 Activation du debugging sur votre Chrome...")
        return self._enable_debugging_existing_chrome()
    
    def _enable_debugging_existing_chrome(self):
        """Active le debugging sur le Chrome existant"""
        try:
            # Créer un script pour lancer Chrome avec debugging
            debug_script = '''
@echo off
echo Fermeture de Chrome...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul
echo Lancement de Chrome avec debugging...
start "" "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%USERPROFILE%\\AppData\\Local\\Google\\Chrome\\User Data"
echo Chrome lancé avec debugging sur le port 9222
timeout /t 3 >nul
'''
            
            with open('enable_chrome_debug.bat', 'w') as f:
                f.write(debug_script)
            
            print("   📄 Script créé: enable_chrome_debug.bat")
            print("   🚀 Lancement de Chrome avec debugging...")
            
            # Exécuter le script
            subprocess.run(['enable_chrome_debug.bat'], shell=True)
            
            # Attendre que Chrome démarre
            time.sleep(5)
            
            # Vérifier si le debugging est actif
            try:
                response = requests.get('http://localhost:9222/json', timeout=5)
                if response.status_code == 200:
                    print("   ✅ Debugging activé avec succès!")
                    return 9222
            except:
                pass
            
            print("   ❌ Impossible d'activer le debugging automatiquement")
            print("   💡 Veuillez fermer Chrome et relancer avec debugging:")
            print("   📝 chrome.exe --remote-debugging-port=9222")
            return None
            
        except Exception as e:
            print(f"   ❌ Erreur activation debugging: {e}")
            return None
    
    def connect_to_existing_chrome(self):
        """Se connecte au Chrome existant avec debugging"""
        print("🔗 Connexion à votre session Chrome...")
        
        # Trouver le port de debugging
        self.debug_port = self.find_existing_chrome_debug_port()
        if not self.debug_port:
            return False
        
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            
            # Options pour éviter les conflits
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print(f"   ✅ Connecté à votre Chrome (port {self.debug_port})")
            print(f"   🌐 URL actuelle: {self.driver.current_url}")
            print(f"   📄 Titre: {self.driver.title}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur connexion: {e}")
            print("   💡 Assurez-vous que Chrome est ouvert avec debugging")
            return False
    
    def navigate_to_target_in_session(self, url):
        """Navigue vers l'URL cible dans votre session Chrome"""
        print(f"🌐 Navigation vers: {url}")
        
        try:
            # Ouvrir dans un nouvel onglet pour préserver votre session
            self.driver.execute_script(f"window.open('{url}', '_blank');")
            
            # Basculer vers le nouvel onglet
            self.driver.switch_to.window(self.driver.window_handles[-1])
            
            time.sleep(5)
            
            print(f"   ✅ Page chargée: {self.driver.title}")
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur navigation: {e}")
            return False
    
    def auto_cloudflare_handler(self):
        """Gestion automatique de Cloudflare dans votre session"""
        print("🛡️ Gestion automatique Cloudflare...")
        
        max_wait = 45
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                title = self.driver.title.lower()
                
                # Vérifier si on est sur une page Cloudflare
                if any(indicator in title for indicator in ["cloudflare", "checking", "moment", "instant", "please wait"]):
                    print("   ⏳ Page Cloudflare détectée - Attente automatique...")
                    
                    # Simulation d'activité humaine légère
                    self._light_human_simulation()
                    time.sleep(3)
                    continue
                
                # Vérifier si on a accès au contenu
                body = self.driver.find_element(By.TAG_NAME, "body")
                if body and len(body.text) > 1000:
                    # Vérifier la présence d'éléments de contenu
                    content_check = self.driver.execute_script("""
                        var indicators = 0;
                        if (document.querySelectorAll('a').length > 10) indicators++;
                        if (document.querySelectorAll('div').length > 20) indicators++;
                        if (document.body.innerText.length > 2000) indicators++;
                        return indicators;
                    """)
                    
                    if content_check >= 2:
                        print("   ✅ Cloudflare passé - Accès au contenu!")
                        return True
                
            except Exception as e:
                print(f"   ⚠️ Erreur vérification: {e}")
            
            time.sleep(2)
        
        print("   ⚠️ Timeout Cloudflare - Extraction forcée")
        return True
    
    def _light_human_simulation(self):
        """Simulation d'activité humaine légère"""
        try:
            # Petit mouvement de souris
            actions = ActionChains(self.driver)
            x = random.randint(-50, 50)
            y = random.randint(-50, 50)
            actions.move_by_offset(x, y)
            actions.move_by_offset(-x, -y)
            actions.perform()
        except:
            pass
    
    def auto_uqload_finder_and_clicker(self):
        """Recherche et clic automatique sur UQload"""
        print("🔍 Recherche automatique UQload...")
        
        try:
            time.sleep(5)
            
            # Recherche exhaustive UQload
            uqload_elements = self.driver.execute_script("""
                var elements = [];
                
                // Sélecteurs UQload complets
                var selectors = [
                    'a[href*="uqload" i]',
                    'button[onclick*="uqload" i]',
                    '[data-server*="uqload" i]',
                    '*[class*="uqload" i]',
                    '*[id*="uqload" i]',
                    '[data-link*="uqload" i]',
                    '[title*="uqload" i]',
                    'a[href*="uq" i]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            var rect = element.getBoundingClientRect();
                            if (rect.width > 5 && rect.height > 5) {
                                elements.push({
                                    text: (element.textContent || element.innerText || '').substring(0, 50),
                                    href: element.href || '',
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                // Recherche par texte
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                var textNode;
                while (textNode = walker.nextNode()) {
                    var text = textNode.textContent.toLowerCase();
                    if (text.includes('uqload') || (text.includes('uq') && text.length < 15)) {
                        var parent = textNode.parentElement;
                        if (parent) {
                            var rect = parent.getBoundingClientRect();
                            if (rect.width > 10 && rect.height > 10) {
                                elements.push({
                                    text: textNode.textContent.substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        }
                    }
                }
                
                return elements;
            """)
            
            print(f"   📍 {len(uqload_elements)} éléments UQload trouvés")
            
            if not uqload_elements:
                print("   ❌ Aucun UQload trouvé")
                return False
            
            # Afficher et cliquer sur le premier UQload
            for i, element in enumerate(uqload_elements[:3]):
                text = element.get('text', '').strip()
                x, y = element['position']['x'], element['position']['y']
                
                print(f"   🎯 Tentative {i+1}: '{text[:20]}' à ({x}, {y})")
                
                # Clic automatique
                success = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        try {{
                            element.click();
                            return true;
                        }} catch(e) {{
                            var event = new MouseEvent('click', {{
                                view: window, bubbles: true, cancelable: true,
                                clientX: {x}, clientY: {y}
                            }});
                            element.dispatchEvent(event);
                            return true;
                        }}
                    }}
                    return false;
                """)
                
                if success:
                    print(f"   ✅ Clic UQload réussi!")
                    time.sleep(5)
                    return True
            
            print("   ❌ Tous les clics UQload ont échoué")
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur UQload: {e}")
            return False
    
    def auto_play_finder_and_clicker(self):
        """Recherche et clic automatique sur play"""
        print("▶️ Recherche automatique bouton play...")
        
        try:
            time.sleep(8)
            
            # Recherche boutons play
            play_elements = self.driver.execute_script("""
                var elements = [];
                
                var selectors = [
                    '.play-button', '.video-play-button', '[class*="play"]',
                    'button[class*="play"]', '[onclick*="play"]', '.vjs-big-play-button',
                    '[title*="play" i]', '.play', '#play', '[data-action="play"]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            var rect = element.getBoundingClientRect();
                            if (rect.width > 20 && rect.height > 20) {
                                elements.push({
                                    text: (element.textContent || element.title || '').substring(0, 30),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                // Positions centrales si pas de bouton trouvé
                if (elements.length === 0) {
                    var centerX = Math.round(window.innerWidth / 2);
                    var centerY = Math.round(window.innerHeight / 2);
                    elements.push({
                        text: 'Centre écran',
                        position: {x: centerX, y: centerY}
                    });
                }
                
                return elements;
            """)
            
            print(f"   ▶️ {len(play_elements)} boutons play trouvés")
            
            # Cliquer sur les boutons play
            for i, element in enumerate(play_elements[:5]):
                text = element.get('text', '').strip()
                x, y = element['position']['x'], element['position']['y']
                
                print(f"   🎯 Tentative play {i+1}: '{text[:15]}' à ({x}, {y})")
                
                success = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.click();
                        return true;
                    }}
                    return false;
                """)
                
                if success:
                    print(f"   ✅ Clic play effectué!")
                    time.sleep(5)
                    
                    # Vérifier si vidéo se charge
                    video_detected = self.driver.execute_script("""
                        var videos = document.querySelectorAll('video');
                        return videos.length > 0;
                    """)
                    
                    if video_detected:
                        print(f"   🎥 Vidéo détectée!")
                        return True
            
            print("   ⚠️ Play non trouvé - Extraction directe")
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur play: {e}")
            return False
    
    def auto_video_extraction(self):
        """Extraction automatique des vidéos"""
        print("🎥 Extraction automatique des vidéos...")
        
        try:
            time.sleep(10)
            video_urls = []
            
            # Méthode 1: Éléments video
            print("   📹 Recherche éléments video...")
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append({
                        'url': src,
                        'method': 'video_element',
                        'type': self._get_video_type(src)
                    })
                    print(f"   📹 Video: {src[:60]}...")
            
            # Méthode 2: Regex dans le code source
            print("   🔍 Analyse code source...")
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and not any(v['url'] == match for v in video_urls):
                        video_urls.append({
                            'url': match,
                            'method': 'regex_source',
                            'type': self._get_video_type(match)
                        })
                        print(f"   📹 Regex: {match[:60]}...")
            
            # Méthode 3: JavaScript avancé
            print("   🔍 Analyse JavaScript...")
            js_videos = self.driver.execute_script("""
                var videos = [];
                
                // Variables globales
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string' && 
                            (value.includes('.mp4') || value.includes('.m3u8') || value.includes('.webm')) &&
                            value.startsWith('http')) {
                            videos.push(value);
                        }
                    } catch(e) {}
                }
                
                return videos;
            """)
            
            for js_video in js_videos:
                if not any(v['url'] == js_video for v in video_urls):
                    video_urls.append({
                        'url': js_video,
                        'method': 'javascript',
                        'type': self._get_video_type(js_video)
                    })
                    print(f"   📹 JavaScript: {js_video[:60]}...")
            
            print(f"\n🎬 {len(video_urls)} liens vidéo trouvés")
            return video_urls
            
        except Exception as e:
            print(f"   ❌ Erreur extraction: {e}")
            return []
    
    def _get_video_type(self, url):
        """Détermine le type de vidéo"""
        url_lower = url.lower()
        if '.mp4' in url_lower:
            return 'mp4'
        elif '.m3u8' in url_lower:
            return 'm3u8'
        elif '.webm' in url_lower:
            return 'webm'
        else:
            return 'unknown'
    
    def save_session_results(self, video_urls, original_url):
        """Sauvegarde les résultats de la session"""
        if not video_urls:
            print("❌ Aucun résultat à sauvegarder")
            return None
        
        # Sélectionner le meilleur
        best_video = max(video_urls, key=lambda v: (
            100 if v['type'] == 'mp4' else 
            80 if v['type'] == 'm3u8' else 
            60 if v['type'] == 'webm' else 0
        ))
        
        result = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'original_url': original_url,
            'current_url': self.driver.current_url,
            'extraction_method': 'chrome_session_extractor',
            'total_videos_found': len(video_urls),
            'best_video': best_video,
            'all_videos': video_urls,
            'success': True
        }
        
        with open('chrome_session_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultats sauvegardés: chrome_session_results.json")
        return result
    
    def display_session_results(self, result):
        """Affiche les résultats de la session"""
        if not result:
            return
        
        print("\n" + "="*80)
        print("🎉 EXTRACTION AUTOMATIQUE DANS VOTRE CHROME RÉUSSIE!")
        print("="*80)
        
        best_video = result['best_video']
        print(f"🎯 MEILLEUR LIEN VIDÉO:")
        print(f"   URL: {best_video['url']}")
        print(f"   Type: {best_video['type'].upper()}")
        print(f"   Méthode: {best_video['method']}")
        
        print(f"\n📊 STATISTIQUES:")
        print(f"   Total vidéos: {result['total_videos_found']}")
        
        if len(result['all_videos']) > 1:
            print(f"\n📹 TOUS LES LIENS ({len(result['all_videos'])}):")
            for i, video in enumerate(result['all_videos'], 1):
                print(f"   {i}. [{video['type'].upper()}] {video['url']}")
        
        print("="*80)
    
    def run_session_extraction(self, url):
        """Lance l'extraction dans votre session Chrome"""
        print("🔗 CHROME SESSION EXTRACTOR - UTILISE VOTRE CHROME")
        print("=" * 70)
        print("👨‍💻 Expert en: Connexion session existante, Automation")
        print("🎯 OBJECTIF: Extraire dans votre Chrome ouvert")
        print(f"🔗 URL: {url}")
        print("=" * 70)
        
        try:
            # Étape 1: Connexion à votre Chrome
            if not self.connect_to_existing_chrome():
                print("❌ Connexion à votre Chrome échouée")
                return False
            
            # Étape 2: Navigation dans votre session
            if not self.navigate_to_target_in_session(url):
                print("❌ Navigation échouée")
                return False
            
            # Étape 3: Gestion Cloudflare automatique
            self.auto_cloudflare_handler()
            
            # Étape 4: Recherche et clic UQload automatique
            uqload_success = self.auto_uqload_finder_and_clicker()
            
            # Étape 5: Recherche et clic play automatique
            if uqload_success:
                self.auto_play_finder_and_clicker()
            
            # Étape 6: Extraction automatique
            video_urls = self.auto_video_extraction()
            
            # Étape 7: Sauvegarde et affichage
            if video_urls:
                result = self.save_session_results(video_urls, url)
                self.display_session_results(result)
                return True
            else:
                print("\n❌ AUCUNE VIDÉO TROUVÉE")
                return False
                
        except Exception as e:
            print(f"\n❌ ERREUR: {e}")
            return False
        
        finally:
            # Ne pas fermer le navigateur (c'est votre session)
            print(f"\n📝 Votre Chrome reste ouvert (votre session préservée)")

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🔗 CHROME SESSION EXTRACTOR")
    print("=" * 50)
    print("🎯 UTILISE VOTRE SESSION CHROME EXISTANTE")
    print("🚀 EXTRACTION 100% AUTOMATIQUE")
    print("💻 PRÉSERVE VOTRE SESSION ET VOS ONGLETS")
    
    extractor = ChromeSessionExtractor()
    success = extractor.run_session_extraction(url)
    
    if success:
        print("\n🏆 EXTRACTION DANS VOTRE CHROME RÉUSSIE!")
    else:
        print("\n❌ Extraction échouée")
        print("💡 Vérifiez que Chrome est ouvert avec debugging")

if __name__ == "__main__":
    main()
