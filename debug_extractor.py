#!/usr/bin/env python3
"""
Debug version to see exactly what the site returns
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json

def debug_extraction():
    """Debug the extraction process step by step"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🔍 DEBUG: French Stream Movie Extractor")
    print("=" * 60)
    print(f"Target URL: {url}")
    
    # Setup Chrome options for debugging
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute stealth script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ WebDriver initialized successfully")
        
        # Load the page
        print(f"📡 Loading page...")
        driver.get(url)
        
        # Wait for page load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        print("✅ Page loaded successfully")
        
        # Additional wait for dynamic content
        time.sleep(5)
        
        # Get page title
        title = driver.title
        print(f"📄 Page Title: {title}")
        
        # Get page source length
        page_source = driver.page_source
        print(f"📊 Page Source Length: {len(page_source)} characters")
        
        # Check for common blocking indicators
        blocking_indicators = [
            "cloudflare",
            "access denied",
            "blocked",
            "captcha",
            "enable javascript",
            "please wait",
            "checking your browser"
        ]
        
        found_blocks = []
        for indicator in blocking_indicators:
            if indicator.lower() in page_source.lower():
                found_blocks.append(indicator)
        
        if found_blocks:
            print(f"⚠️  Blocking indicators found: {', '.join(found_blocks)}")
        else:
            print("✅ No obvious blocking indicators detected")
        
        # Look for movie title in various ways
        print("\n🔍 Searching for movie title...")
        
        title_selectors = [
            'h1',
            '.entry-title',
            '.post-title',
            '.movie-title',
            '.film-title',
            '[class*="title"]'
        ]
        
        movie_title = None
        for selector in title_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    if text and len(text) > 5:  # Reasonable title length
                        movie_title = text
                        print(f"📽️  Found title with '{selector}': {text}")
                        break
                if movie_title:
                    break
            except Exception as e:
                continue
        
        if not movie_title:
            print("❌ No movie title found")
        
        # Look for any links
        print("\n🔗 Analyzing all links on page...")
        
        all_links = driver.find_elements(By.TAG_NAME, "a")
        print(f"📊 Total links found: {len(all_links)}")
        
        # Categorize links
        download_services = ['mega.nz', '1fichier', 'uptobox', 'mediafire', 'rapidgator', 'turbobit']
        streaming_services = ['player', 'stream', 'embed', 'video']
        
        download_links = []
        streaming_links = []
        other_links = []
        
        for link in all_links:
            try:
                href = link.get_attribute("href")
                if href:
                    href_lower = href.lower()
                    
                    # Check for download services
                    if any(service in href_lower for service in download_services):
                        download_links.append(href)
                        print(f"📥 Download link: {href}")
                    
                    # Check for streaming services
                    elif any(service in href_lower for service in streaming_services):
                        streaming_links.append(href)
                        print(f"📺 Streaming link: {href}")
                    
                    else:
                        other_links.append(href)
            except:
                continue
        
        print(f"\n📊 Link Summary:")
        print(f"   📥 Download links: {len(download_links)}")
        print(f"   📺 Streaming links: {len(streaming_links)}")
        print(f"   🔗 Other links: {len(other_links)}")
        
        # Look for iframes
        print("\n🖼️  Checking for iframes...")
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"📊 Total iframes found: {len(iframes)}")
        
        for i, iframe in enumerate(iframes):
            try:
                src = iframe.get_attribute("src")
                if src:
                    print(f"   {i+1}. {src}")
            except:
                continue
        
        # Save a sample of the page source for analysis
        print("\n💾 Saving page source sample...")
        
        # Get first 2000 characters of page source
        source_sample = page_source[:2000]
        
        debug_result = {
            "url": url,
            "title": title,
            "movie_title": movie_title,
            "page_source_length": len(page_source),
            "blocking_indicators": found_blocks,
            "total_links": len(all_links),
            "download_links": download_links,
            "streaming_links": streaming_links,
            "total_iframes": len(iframes),
            "source_sample": source_sample
        }
        
        with open("debug_results.json", "w", encoding="utf-8") as f:
            json.dump(debug_result, f, indent=2, ensure_ascii=False)
        
        print("💾 Debug results saved to debug_results.json")
        
        # Final assessment
        print(f"\n{'='*60}")
        print("🎯 EXTRACTION ASSESSMENT:")
        print(f"{'='*60}")
        
        if download_links or streaming_links:
            print("✅ SUCCESS: Found download/streaming links!")
            if download_links:
                print(f"🎯 Primary download link: {download_links[0]}")
        else:
            print("⚠️  No download/streaming links found")
            print("💡 Possible reasons:")
            print("   • Site requires user interaction (clicking buttons)")
            print("   • Links are loaded via additional JavaScript calls")
            print("   • Content is behind login/verification")
            print("   • Anti-bot protection is active")
        
        return debug_result
        
    except Exception as e:
        print(f"❌ Error during debug extraction: {e}")
        return None
        
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    debug_extraction()
