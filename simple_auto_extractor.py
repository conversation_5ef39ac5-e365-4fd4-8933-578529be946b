#!/usr/bin/env python3
"""
SIMPLE AUTO EXTRACTOR - SOLUTION DIRECTE
Expert en: Automation simple et efficace, Extraction directe
OBJECTIF: Lancer Chrome et extraire automatiquement le lien vidéo
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re
import random

class SimpleAutoExtractor:
    """Extracteur automatique simple et efficace"""
    
    def __init__(self):
        self.driver = None
        
    def setup_simple_browser(self):
        """Configuration navigateur simple mais efficace"""
        print("🚀 Configuration navigateur automatique...")
        
        chrome_options = Options()
        
        # Options essentielles pour automation
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Prefs simples
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Script anti-détection simple
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined});")
            
            self.driver.set_window_size(1366, 768)
            
            print("   ✅ Navigateur configuré")
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            return False
    
    def navigate_and_wait(self, url):
        """Navigation avec attente intelligente"""
        print(f"🌐 Navigation vers: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            print(f"   ✅ Page chargée: {self.driver.title}")
            
            # Attente Cloudflare simple
            print("   ⏳ Attente Cloudflare...")
            max_wait = 30
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                try:
                    title = self.driver.title.lower()
                    if not any(indicator in title for indicator in ["cloudflare", "checking", "moment", "instant"]):
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:
                            print("   ✅ Contenu accessible!")
                            return True
                except:
                    pass
                
                time.sleep(2)
            
            print("   ⚠️ Timeout - Extraction forcée")
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur navigation: {e}")
            return False
    
    def find_and_click_uqload(self):
        """Recherche et clic UQload simple"""
        print("🔍 Recherche UQload...")
        
        try:
            time.sleep(5)
            
            # Recherche simple mais efficace
            uqload_found = False
            
            # Méthode 1: Recherche par texte visible
            try:
                elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload') or contains(text(), 'UQ')]")
                for element in elements:
                    if element.is_displayed():
                        print(f"   🎯 UQload trouvé: {element.text[:30]}")
                        element.click()
                        uqload_found = True
                        break
            except:
                pass
            
            # Méthode 2: Recherche par liens
            if not uqload_found:
                try:
                    links = self.driver.find_elements(By.TAG_NAME, "a")
                    for link in links:
                        href = link.get_attribute("href") or ""
                        text = link.text.lower()
                        if "uqload" in href.lower() or "uqload" in text:
                            print(f"   🎯 Lien UQload trouvé: {text[:30]}")
                            link.click()
                            uqload_found = True
                            break
                except:
                    pass
            
            # Méthode 3: Recherche par coordonnées probables
            if not uqload_found:
                print("   🎯 Test coordonnées probables...")
                probable_coords = [
                    (400, 500), (350, 450), (450, 550),
                    (300, 400), (500, 600), (250, 350)
                ]
                
                for x, y in probable_coords:
                    try:
                        element = self.driver.execute_script(f"return document.elementFromPoint({x}, {y});")
                        if element:
                            text = element.get_attribute("textContent") or ""
                            if "uqload" in text.lower() or "uq" in text.lower():
                                print(f"   🎯 UQload par coordonnées: ({x}, {y})")
                                element.click()
                                uqload_found = True
                                break
                    except:
                        continue
            
            if uqload_found:
                print("   ✅ UQload cliqué!")
                time.sleep(5)
                return True
            else:
                print("   ❌ UQload non trouvé")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur UQload: {e}")
            return False
    
    def find_and_click_play(self):
        """Recherche et clic play simple"""
        print("▶️ Recherche bouton play...")
        
        try:
            time.sleep(8)
            
            play_found = False
            
            # Méthode 1: Boutons play visibles
            try:
                play_selectors = [
                    "button[class*='play']", ".play-button", ".video-play-button",
                    "[onclick*='play']", ".vjs-big-play-button"
                ]
                
                for selector in play_selectors:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"   ▶️ Bouton play trouvé: {selector}")
                            element.click()
                            play_found = True
                            break
                    if play_found:
                        break
            except:
                pass
            
            # Méthode 2: Clic centre écran
            if not play_found:
                print("   ▶️ Clic centre écran...")
                try:
                    center_x = self.driver.execute_script("return window.innerWidth / 2;")
                    center_y = self.driver.execute_script("return window.innerHeight / 2;")
                    
                    actions = ActionChains(self.driver)
                    actions.move_by_offset(center_x - 400, center_y - 300)
                    actions.click()
                    actions.perform()
                    
                    play_found = True
                except:
                    pass
            
            if play_found:
                print("   ✅ Play cliqué!")
                time.sleep(8)
                return True
            else:
                print("   ⚠️ Play non trouvé - Extraction directe")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur play: {e}")
            return False
    
    def extract_video_links(self):
        """Extraction simple des liens vidéo"""
        print("🎥 Extraction liens vidéo...")
        
        try:
            time.sleep(10)
            video_urls = []
            
            # Méthode 1: Éléments video
            print("   📹 Recherche éléments video...")
            try:
                videos = self.driver.find_elements(By.TAG_NAME, "video")
                for video in videos:
                    src = video.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append({
                            'url': src,
                            'method': 'video_element',
                            'type': self._get_video_type(src)
                        })
                        print(f"   📹 Video: {src[:60]}...")
                    
                    # Sources dans video
                    sources = video.find_elements(By.TAG_NAME, "source")
                    for source in sources:
                        src = source.get_attribute('src')
                        if src and src.startswith('http'):
                            video_urls.append({
                                'url': src,
                                'method': 'video_source',
                                'type': self._get_video_type(src)
                            })
                            print(f"   📹 Source: {src[:60]}...")
            except:
                pass
            
            # Méthode 2: Regex simple dans le code source
            print("   🔍 Analyse code source...")
            try:
                page_source = self.driver.page_source
                
                # Patterns simples mais efficaces
                patterns = [
                    r'"file":\s*"([^"]+\.mp4[^"]*)"',
                    r'"src":\s*"([^"]+\.mp4[^"]*)"',
                    r'"file":\s*"([^"]+\.m3u8[^"]*)"',
                    r'"src":\s*"([^"]+\.m3u8[^"]*)"',
                    r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?',
                    r'https?://[^"\s]+\.m3u8(?:\?[^"\s]*)?'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, page_source, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]
                        if match and match.startswith('http') and not any(v['url'] == match for v in video_urls):
                            video_urls.append({
                                'url': match,
                                'method': 'regex',
                                'type': self._get_video_type(match)
                            })
                            print(f"   📹 Regex: {match[:60]}...")
            except:
                pass
            
            # Méthode 3: JavaScript simple
            print("   🔍 Analyse JavaScript...")
            try:
                js_videos = self.driver.execute_script("""
                    var videos = [];
                    
                    // Chercher dans les variables window
                    for (var prop in window) {
                        try {
                            var value = window[prop];
                            if (typeof value === 'string' && 
                                (value.includes('.mp4') || value.includes('.m3u8')) &&
                                value.startsWith('http')) {
                                videos.push(value);
                            }
                        } catch(e) {}
                    }
                    
                    return videos;
                """)
                
                for js_video in js_videos:
                    if not any(v['url'] == js_video for v in video_urls):
                        video_urls.append({
                            'url': js_video,
                            'method': 'javascript',
                            'type': self._get_video_type(js_video)
                        })
                        print(f"   📹 JS: {js_video[:60]}...")
            except:
                pass
            
            # Supprimer doublons
            unique_videos = []
            seen_urls = set()
            for video in video_urls:
                if video['url'] not in seen_urls:
                    unique_videos.append(video)
                    seen_urls.add(video['url'])
            
            print(f"\n🎬 {len(unique_videos)} liens vidéo trouvés")
            return unique_videos
            
        except Exception as e:
            print(f"   ❌ Erreur extraction: {e}")
            return []
    
    def _get_video_type(self, url):
        """Type de vidéo"""
        url_lower = url.lower()
        if '.mp4' in url_lower:
            return 'mp4'
        elif '.m3u8' in url_lower:
            return 'm3u8'
        elif '.webm' in url_lower:
            return 'webm'
        else:
            return 'unknown'
    
    def save_simple_results(self, video_urls, original_url):
        """Sauvegarde simple"""
        if not video_urls:
            print("❌ Aucun résultat")
            return None
        
        # Meilleur vidéo (MP4 prioritaire)
        best_video = max(video_urls, key=lambda v: (
            100 if v['type'] == 'mp4' else 
            80 if v['type'] == 'm3u8' else 60
        ))
        
        result = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'original_url': original_url,
            'current_url': self.driver.current_url,
            'extraction_method': 'simple_auto_extractor',
            'total_videos': len(video_urls),
            'best_video': best_video,
            'all_videos': video_urls,
            'success': True
        }
        
        with open('simple_auto_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        return result
    
    def display_simple_results(self, result):
        """Affichage simple"""
        if not result:
            return
        
        print("\n" + "="*70)
        print("🎉 EXTRACTION AUTOMATIQUE SIMPLE RÉUSSIE!")
        print("="*70)
        
        best = result['best_video']
        print(f"🎯 LIEN VIDÉO PRINCIPAL:")
        print(f"   {best['url']}")
        print(f"   Type: {best['type'].upper()}")
        
        if len(result['all_videos']) > 1:
            print(f"\n📹 TOUS LES LIENS ({len(result['all_videos'])}):")
            for i, video in enumerate(result['all_videos'], 1):
                print(f"   {i}. [{video['type'].upper()}] {video['url']}")
        
        print(f"\n💾 Résultats: simple_auto_results.json")
        print("="*70)
    
    def run_simple_extraction(self, url):
        """Extraction automatique simple"""
        print("🚀 SIMPLE AUTO EXTRACTOR - SOLUTION DIRECTE")
        print("=" * 60)
        print("🎯 EXTRACTION 100% AUTOMATIQUE SIMPLE")
        print(f"🔗 URL: {url}")
        print("=" * 60)
        
        try:
            # Étape 1: Configuration
            if not self.setup_simple_browser():
                return False
            
            # Étape 2: Navigation
            if not self.navigate_and_wait(url):
                return False
            
            # Étape 3: UQload
            uqload_success = self.find_and_click_uqload()
            
            # Étape 4: Play
            if uqload_success:
                self.find_and_click_play()
            
            # Étape 5: Extraction
            video_urls = self.extract_video_links()
            
            # Étape 6: Résultats
            if video_urls:
                result = self.save_simple_results(video_urls, url)
                self.display_simple_results(result)
                return True
            else:
                print("\n❌ AUCUNE VIDÉO TROUVÉE")
                return False
                
        except Exception as e:
            print(f"\n❌ ERREUR: {e}")
            return False
        
        finally:
            if self.driver:
                print(f"\n⏳ Navigateur reste ouvert...")
                input("Appuyez sur Entrée pour fermer...")
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🚀 SIMPLE AUTO EXTRACTOR")
    print("=" * 40)
    print("🎯 SOLUTION SIMPLE ET EFFICACE")
    print("🔥 EXTRACTION 100% AUTOMATIQUE")
    
    extractor = SimpleAutoExtractor()
    success = extractor.run_simple_extraction(url)
    
    if success:
        print("\n🏆 EXTRACTION AUTOMATIQUE RÉUSSIE!")
    else:
        print("\n❌ Extraction échouée")

if __name__ == "__main__":
    main()
