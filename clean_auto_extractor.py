#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLEAN AUTO EXTRACTOR - 100% AUTOMATIQUE
Extraction entierement automatique sans intervention
"""

import time
import json
import re
import sys
import os

def extract_automatically():
    print("DEMARRAGE EXTRACTION AUTOMATIQUE")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        print("Modules importes avec succes")
    except ImportError as e:
        print(f"Erreur import: {e}")
        return False
    
    # Configuration Chrome automatique
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    
    driver = None
    try:
        # Lancer Chrome
        print("Lancement Chrome automatique...")
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except:
            driver = webdriver.Chrome(options=chrome_options)
        
        print("Chrome lance avec succes")
        
        # Navigation automatique
        url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
        print(f"Navigation automatique vers: {url}")
        driver.get(url)
        time.sleep(5)
        
        print(f"Page chargee: {driver.title}")
        
        # Attente Cloudflare automatique
        print("Attente Cloudflare automatique...")
        for i in range(30):
            try:
                title = driver.title.lower()
                if not any(word in title for word in ["cloudflare", "checking", "moment", "instant"]):
                    body = driver.find_element(By.TAG_NAME, "body")
                    if len(body.text) > 1000:
                        print("Cloudflare passe automatiquement!")
                        break
            except:
                pass
            time.sleep(2)
            if i % 5 == 0:
                print(f"   Attente... {i*2}s")
        
        # Recherche UQload automatique
        print("Recherche UQload automatique...")
        uqload_found = False
        
        # Methode 1: Par texte
        try:
            elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload') or contains(text(), 'UQ')]")
            print(f"Elements UQload trouves: {len(elements)}")
            
            for element in elements:
                try:
                    if element.is_displayed():
                        text = element.text.strip()
                        print(f"UQload trouve automatiquement: {text[:30]}")
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)
                        driver.execute_script("arguments[0].click();", element)
                        uqload_found = True
                        time.sleep(5)
                        print("UQload clique avec succes!")
                        break
                except:
                    continue
        except Exception as e:
            print(f"Erreur recherche UQload par texte: {e}")
        
        # Methode 2: Par liens
        if not uqload_found:
            try:
                links = driver.find_elements(By.TAG_NAME, "a")
                print(f"Liens trouves: {len(links)}")
                
                for link in links:
                    try:
                        href = link.get_attribute("href") or ""
                        text = link.text.lower()
                        
                        if "uqload" in href.lower() or "uqload" in text:
                            print(f"Lien UQload trouve: {text[:30]}")
                            driver.execute_script("arguments[0].click();", link)
                            uqload_found = True
                            time.sleep(5)
                            print("Lien UQload clique avec succes!")
                            break
                    except:
                        continue
            except Exception as e:
                print(f"Erreur recherche UQload par liens: {e}")
        
        # Methode 3: Par coordonnees probables
        if not uqload_found:
            print("Test coordonnees UQload automatique...")
            coords = [(400, 500), (350, 450), (450, 550), (300, 400), (500, 600)]
            
            for x, y in coords:
                try:
                    element = driver.execute_script(f"return document.elementFromPoint({x}, {y});")
                    if element:
                        text = element.get_attribute("textContent") or ""
                        if "uqload" in text.lower() or "uq" in text.lower():
                            print(f"UQload par coordonnees: ({x}, {y})")
                            driver.execute_script("arguments[0].click();", element)
                            uqload_found = True
                            time.sleep(5)
                            print("UQload coordonnees clique avec succes!")
                            break
                except:
                    continue
        
        print(f"UQload trouve: {uqload_found}")
        
        # Clic play automatique
        print("Clic play automatique...")
        time.sleep(8)
        
        # Methode 1: Boutons play
        play_found = False
        try:
            selectors = [
                ".play-button", ".video-play-button", "[class*='play']", 
                "button[class*='play']", "[onclick*='play']", ".vjs-big-play-button"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"Play trouve automatiquement: {selector}")
                            driver.execute_script("arguments[0].click();", element)
                            play_found = True
                            time.sleep(3)
                            print("Play clique avec succes!")
                            break
                    if play_found:
                        break
                except:
                    continue
        except Exception as e:
            print(f"Erreur recherche play: {e}")
        
        # Methode 2: Clic centre automatique
        if not play_found:
            print("Clic centre automatique...")
            try:
                center_x = driver.execute_script("return window.innerWidth / 2;")
                center_y = driver.execute_script("return window.innerHeight / 2;")
                
                driver.execute_script(f"""
                    var element = document.elementFromPoint({center_x}, {center_y});
                    if (element) {{
                        element.click();
                        console.log('Clic centre effectue');
                    }}
                """)
                
                print("Clic centre effectue automatiquement")
                time.sleep(5)
            except Exception as e:
                print(f"Erreur clic centre: {e}")
        
        # Attendre chargement video
        print("Attente chargement video...")
        time.sleep(10)
        
        # Extraction video automatique
        print("Extraction video automatique...")
        video_urls = []
        
        # Methode 1: Elements video
        try:
            print("Recherche elements video...")
            videos = driver.find_elements(By.TAG_NAME, "video")
            print(f"Elements video trouves: {len(videos)}")
            
            for video in videos:
                try:
                    src = video.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append(src)
                        print(f"Video src: {src[:60]}...")
                    
                    sources = video.find_elements(By.TAG_NAME, "source")
                    for source in sources:
                        src = source.get_attribute('src')
                        if src and src.startswith('http'):
                            video_urls.append(src)
                            print(f"Video source: {src[:60]}...")
                except:
                    continue
        except Exception as e:
            print(f"Erreur elements video: {e}")
        
        # Methode 2: Regex automatique
        try:
            print("Analyse code source...")
            page_source = driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.mp4[^"]*)"',
                r'"src":\s*"([^"]+\.mp4[^"]*)"',
                r'"file":\s*"([^"]+\.m3u8[^"]*)"',
                r'"src":\s*"([^"]+\.m3u8[^"]*)"',
                r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?',
                r'https?://[^"\s]+\.m3u8(?:\?[^"\s]*)?',
                r'https?://[^"\s]+\.webm(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, page_source, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]
                        if match and match.startswith('http') and match not in video_urls:
                            video_urls.append(match)
                            print(f"Regex: {match[:60]}...")
                except:
                    continue
        except Exception as e:
            print(f"Erreur regex: {e}")
        
        # Methode 3: JavaScript automatique
        try:
            print("Analyse JavaScript...")
            js_videos = driver.execute_script("""
                var videos = [];
                
                // Variables globales
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string' && 
                            (value.includes('.mp4') || value.includes('.m3u8') || value.includes('.webm')) &&
                            value.startsWith('http') && value.length < 500) {
                            videos.push(value);
                        }
                    } catch(e) {}
                }
                
                return videos;
            """)
            
            for js_video in js_videos:
                if js_video not in video_urls:
                    video_urls.append(js_video)
                    print(f"JavaScript: {js_video[:60]}...")
        except Exception as e:
            print(f"Erreur JavaScript: {e}")
        
        # Supprimer doublons et filtrer
        unique_videos = []
        seen = set()
        
        for url in video_urls:
            if url not in seen and len(url) > 10:
                if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.webm', 'video', 'stream']):
                    unique_videos.append(url)
                    seen.add(url)
        
        print(f"Videos uniques trouvees: {len(unique_videos)}")
        
        # Resultats automatiques
        if unique_videos:
            # Trier par priorite
            def priority(url):
                url_lower = url.lower()
                if '.mp4' in url_lower:
                    return 1
                elif '.m3u8' in url_lower:
                    return 2
                elif '.webm' in url_lower:
                    return 3
                else:
                    return 4
            
            unique_videos.sort(key=priority)
            
            result = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'url': url,
                'total_videos': len(unique_videos),
                'primary_video': unique_videos[0],
                'all_videos': unique_videos,
                'uqload_found': uqload_found,
                'method': 'clean_automatic',
                'success': True
            }
            
            # Sauvegarde automatique
            with open('clean_auto_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print("\n" + "="*70)
            print("EXTRACTION ENTIEREMENT AUTOMATIQUE REUSSIE!")
            print("="*70)
            print(f"LIEN VIDEO PRINCIPAL:")
            print(f"   {result['primary_video']}")
            
            if len(unique_videos) > 1:
                print(f"\nTOUS LES LIENS ({len(unique_videos)}):")
                for i, video in enumerate(unique_videos, 1):
                    video_type = "MP4" if ".mp4" in video.lower() else "M3U8" if ".m3u8" in video.lower() else "WebM" if ".webm" in video.lower() else "Autre"
                    print(f"   {i}. [{video_type}] {video}")
            
            print(f"\nResultats: clean_auto_result.json")
            print("="*70)
            
            return True
        else:
            print("\nAUCUNE VIDEO TROUVEE AUTOMATIQUEMENT")
            return False
    
    except Exception as e:
        print(f"\nERREUR AUTOMATIQUE: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if driver:
            print("\nFermeture automatique dans 15 secondes...")
            time.sleep(15)
            try:
                driver.quit()
                print("Navigateur ferme automatiquement")
            except:
                print("Erreur fermeture navigateur")

if __name__ == "__main__":
    print("CLEAN AUTO EXTRACTOR - 100% AUTOMATIQUE")
    print("=" * 60)
    print("EXTRACTION ENTIEREMENT AUTOMATIQUE")
    print("AUCUNE INTERVENTION MANUELLE")
    print()
    
    success = extract_automatically()
    
    if success:
        print("\nEXTRACTION ENTIEREMENT AUTOMATIQUE REUSSIE!")
        print("Lien video extrait sans intervention!")
    else:
        print("\nExtraction automatique echouee")
    
    print("\nFIN DE L'EXTRACTION AUTOMATIQUE")
