#!/usr/bin/env python3
"""
EXTRACTEUR ULTIMATE PAR COORDONNÉES
1. Clique aux coordonnées fixes de Cloudflare
2. Trouve et clique UQload par coordonnées
3. Clique play par coordonnées  
4. Extrait le lien vidéo
MÉTHODE COORDONNÉES = PLUS RAPIDE ET FIABLE !
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import random
import json
import re

class CoordinateUQloadExtractor:
    """Extracteur UQload utilisant les coordonnées fixes"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        
        # Coordonnées pour différents éléments
        self.cloudflare_coords = [
            (150, 300), (100, 250), (200, 350), (120, 280), (180, 320)
        ]
        
        self.uqload_coords = [
            (400, 500), (350, 450), (450, 550), (300, 400), (500, 600),
            (250, 350), (550, 650), (200, 300), (600, 700)
        ]
        
        self.play_coords = [
            (683, 384),  # Centre écran standard
            (650, 350), (700, 400), (600, 300), (750, 450),
            (550, 250), (800, 500), (500, 200), (850, 550)
        ]
    
    def setup_coordinate_driver(self):
        """Configure le driver pour les coordonnées"""
        print("🎯 Configuration navigateur COORDONNÉES...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Taille FIXE pour coordonnées précises
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur coordonnées configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def click_coordinates(self, x, y, description=""):
        """Clique aux coordonnées avec méthodes multiples"""
        try:
            print(f"🖱️ Clic ({x}, {y}) - {description}")
            
            # Méthode 1: ActionChains
            try:
                actions = ActionChains(self.driver)
                actions.move_by_offset(x, y).click().perform()
                actions.move_by_offset(-x, -y).perform()  # Reset
                time.sleep(random.uniform(1, 2))
                return True
            except:
                pass
            
            # Méthode 2: JavaScript
            try:
                self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.click();
                    }}
                """)
                time.sleep(random.uniform(1, 2))
                return True
            except:
                pass
            
            # Méthode 3: Event dispatch
            try:
                self.driver.execute_script(f"""
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.dispatchEvent(event);
                    }}
                """)
                time.sleep(random.uniform(1, 2))
                return True
            except:
                pass
            
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            return False
    
    def bypass_cloudflare_coordinates(self):
        """Contourne Cloudflare par coordonnées"""
        print("🛡️ Contournement Cloudflare par COORDONNÉES...")
        
        # Vérifier si déjà passé
        try:
            current_title = self.driver.title.lower()
            if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare"]):
                body = self.driver.find_element(By.TAG_NAME, "body")
                if body and len(body.text) > 1000:
                    print("   ✅ Déjà passé!")
                    return True
        except:
            pass
        
        # Essayer toutes les coordonnées Cloudflare
        for i, (x, y) in enumerate(self.cloudflare_coords, 1):
            print(f"   🎯 Tentative {i}/{len(self.cloudflare_coords)}")
            
            if self.click_coordinates(x, y, "Case Cloudflare"):
                time.sleep(3)
                
                # Vérifier si passé
                try:
                    current_title = self.driver.title.lower()
                    if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare"]):
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:
                            print(f"   ✅ Cloudflare passé avec ({x}, {y})!")
                            return True
                except:
                    pass
        
        print("   ⚠️ Cloudflare non passé - continuons quand même")
        return True  # Continuer même si pas sûr
    
    def find_uqload_coordinates(self):
        """Trouve UQload par coordonnées"""
        print("🔍 Recherche UQload par COORDONNÉES...")
        
        # Attendre le chargement
        time.sleep(5)
        
        # Essayer toutes les coordonnées UQload
        for i, (x, y) in enumerate(self.uqload_coords, 1):
            print(f"   🎯 Test UQload {i}/{len(self.uqload_coords)} - ({x}, {y})")
            
            # Vérifier s'il y a un élément cliquable à ces coordonnées
            try:
                element_info = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        return {{
                            tagName: element.tagName,
                            text: element.textContent || element.innerText || '',
                            href: element.href || '',
                            onclick: element.onclick ? element.onclick.toString() : ''
                        }};
                    }}
                    return null;
                """)
                
                if element_info:
                    text = element_info.get('text', '').lower()
                    href = element_info.get('href', '').lower()
                    onclick = element_info.get('onclick', '').lower()
                    
                    # Vérifier si c'est UQload
                    if any('uqload' in attr for attr in [text, href, onclick]):
                        print(f"   ✅ UQload trouvé à ({x}, {y})!")
                        return (x, y)
                
            except:
                continue
        
        print("   ❌ UQload non trouvé par coordonnées")
        return None
    
    def click_play_coordinates(self):
        """Clique sur play par coordonnées"""
        print("▶️ Clic play par COORDONNÉES...")
        
        # Attendre le chargement du lecteur
        time.sleep(5)
        
        # Essayer toutes les coordonnées de play
        for i, (x, y) in enumerate(self.play_coords, 1):
            print(f"   ▶️ Test play {i}/{len(self.play_coords)} - ({x}, {y})")
            
            if self.click_coordinates(x, y, "Bouton play"):
                time.sleep(3)
                
                # Vérifier si une vidéo se charge
                try:
                    videos = self.driver.find_elements(By.TAG_NAME, "video")
                    if videos:
                        for video in videos:
                            if video.get_attribute('src') or video.find_elements(By.TAG_NAME, "source"):
                                print(f"   ✅ Play réussi à ({x}, {y})!")
                                return True
                except:
                    pass
        
        print("   ⚠️ Play non trouvé - extraction directe")
        return False
    
    def extract_video_coordinates(self):
        """Extrait les liens vidéo"""
        print("🎥 Extraction vidéo...")
        
        time.sleep(8)
        video_urls = []
        
        try:
            # Méthode 1: Éléments video
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"   📹 Video: {src}")
            
            # Méthode 2: Regex dans le code source
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"   📹 Regex: {match}")
            
            # Méthode 3: Network
            try:
                network_urls = self.driver.execute_script("""
                    var urls = [];
                    if (window.performance && window.performance.getEntriesByType) {
                        var entries = window.performance.getEntriesByType('resource');
                        for (var i = 0; i < entries.length; i++) {
                            var url = entries[i].name;
                            if (url.includes('.mp4') || url.includes('.m3u8') || url.includes('video')) {
                                urls.push(url);
                            }
                        }
                    }
                    return urls;
                """)
                
                for url in network_urls:
                    if url not in video_urls and url.startswith('http'):
                        video_urls.append(url)
                        print(f"   📹 Network: {url}")
                        
            except Exception as e:
                print(f"   ⚠️ Erreur network: {e}")
            
        except Exception as e:
            print(f"   ⚠️ Erreur extraction: {e}")
        
        return list(set(video_urls))
    
    def run_coordinate_extraction(self, url):
        """Lance l'extraction complète par coordonnées"""
        print("🎯 EXTRACTEUR ULTIMATE PAR COORDONNÉES")
        print("=" * 70)
        print("💡 STRATÉGIE: Coordonnées fixes pour tout!")
        print(f"🔗 URL: {url}")
        print()
        
        try:
            # Étape 1: Setup
            if not self.setup_coordinate_driver():
                return None
            
            # Étape 2: Charger la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            time.sleep(5)
            
            # Étape 3: Contourner Cloudflare
            if not self.bypass_cloudflare_coordinates():
                print("❌ Cloudflare non contourné")
                # Continuer quand même
            
            # Étape 4: Trouver UQload
            uqload_coords = self.find_uqload_coordinates()
            if not uqload_coords:
                print("❌ UQload non trouvé par coordonnées")
                return None
            
            # Étape 5: Cliquer sur UQload
            print("🖱️ Clic sur UQload...")
            x, y = uqload_coords
            if not self.click_coordinates(x, y, "UQload confirmé"):
                print("❌ Impossible de cliquer UQload")
                return None
            
            print("✅ UQload cliqué!")
            time.sleep(5)
            
            # Étape 6: Cliquer sur play
            self.click_play_coordinates()
            
            # Étape 7: Extraire les liens
            video_urls = self.extract_video_coordinates()
            
            if video_urls:
                result = {
                    'url': url,
                    'video_links': video_urls,
                    'primary_link': video_urls[0],
                    'extraction_method': 'coordinate_based',
                    'success': True,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'uqload_coordinates': uqload_coords
                }
                
                # Sauvegarder
                with open('coordinate_extraction_result.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("\n" + "="*70)
                print("🎉 EXTRACTION PAR COORDONNÉES RÉUSSIE!")
                print("="*70)
                
                print(f"🎯 LIEN VIDÉO PRINCIPAL:")
                print(f"   {result['primary_link']}")
                
                if len(video_urls) > 1:
                    print(f"\n📹 TOUS LES LIENS ({len(video_urls)}):")
                    for i, link in enumerate(video_urls, 1):
                        print(f"   {i}. {link}")
                
                print(f"\n📍 UQload trouvé aux coordonnées: {uqload_coords}")
                print(f"💾 Résultats: coordinate_extraction_result.json")
                
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur fatale: {e}")
            return None
        
        finally:
            if self.driver:
                print("\n⏳ Fermeture dans 10 secondes...")
                time.sleep(10)
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎯 EXTRACTEUR PAR COORDONNÉES")
    print("=" * 70)
    print("💡 IDÉE GÉNIALE: Coordonnées fixes pour TOUT!")
    print("🚀 Plus rapide, plus fiable, plus simple!")
    print()
    
    extractor = CoordinateUQloadExtractor(show_browser=True)
    result = extractor.run_coordinate_extraction(url)
    
    if result and result['success']:
        print(f"\n🏆 MISSION COORDONNÉES ACCOMPLIE!")
        print(f"🎬 Lien vidéo extrait par coordonnées:")
        print(f"🔗 {result['primary_link']}")
    else:
        print(f"\n❌ Extraction par coordonnées échouée")
        print(f"💡 Ajustez les coordonnées dans le script")

if __name__ == "__main__":
    main()
