#!/usr/bin/env python3
"""
Guide manuel pour extraire le lien UQload
Avec instructions étape par étape
"""

import webbrowser
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def open_browser_with_instructions():
    """Ouvre le navigateur avec des instructions détaillées"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎬 Guide Manuel - Extraction UQload")
    print("=" * 50)
    print()
    print("📋 INSTRUCTIONS ÉTAPE PAR ÉTAPE:")
    print()
    print("1. 🌐 Je vais ouvrir le site dans votre navigateur")
    print("2. ⏳ Attendez que Cloudflare vous laisse passer (5-10 secondes)")
    print("3. 🔍 Cherchez le bouton 'UQload' (encerclé en rouge)")
    print("4. 🖱️ Cliquez sur le bouton 'UQload'")
    print("5. ▶️ Cliquez sur le bouton PLAY bleu")
    print("6. 🎥 La vidéo va se charger")
    print("7. 🔗 Faites clic droit sur la vidéo → 'Copier l'adresse de la vidéo'")
    print()
    
    input("Appuyez sur Entrée pour ouvrir le site...")
    
    # Ouvrir le site
    webbrowser.open(url)
    
    print("✅ Site ouvert dans votre navigateur!")
    print()
    print("🔍 COMMENT TROUVER LE BOUTON UQLOAD:")
    print("   • Cherchez dans la zone des lecteurs vidéo")
    print("   • Il peut être dans un onglet 'Serveurs' ou 'Lecteurs'")
    print("   • Parfois il faut scroller vers le bas")
    print("   • Le bouton peut dire 'UQload', 'UQ', ou avoir un logo UQload")
    print()
    print("▶️ APRÈS AVOIR CLIQUÉ SUR UQLOAD:")
    print("   • Une nouvelle page/iframe va s'ouvrir")
    print("   • Vous verrez un lecteur vidéo avec un bouton PLAY bleu")
    print("   • Cliquez sur PLAY pour démarrer la vidéo")
    print()
    print("🎥 POUR EXTRAIRE LE LIEN VIDÉO:")
    print("   • Clic droit sur la vidéo qui joue")
    print("   • Sélectionnez 'Copier l'adresse de la vidéo' ou 'Copy video URL'")
    print("   • Le lien sera copié dans votre presse-papier")
    print()
    
    # Attendre que l'utilisateur termine
    input("Appuyez sur Entrée quand vous avez copié le lien vidéo...")
    
    # Essayer de récupérer le lien du presse-papier
    try:
        video_link = pyperclip.paste()
        if video_link and ('http' in video_link) and any(ext in video_link.lower() for ext in ['.mp4', '.m3u8', 'video', 'stream']):
            print("✅ Lien vidéo détecté dans le presse-papier!")
            print(f"🎯 LIEN VIDÉO: {video_link}")
            
            # Sauvegarder le lien
            with open('video_link_manual.txt', 'w', encoding='utf-8') as f:
                f.write(f"URL du film: {url}\n")
                f.write(f"Lien vidéo: {video_link}\n")
                f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            print("💾 Lien sauvegardé dans 'video_link_manual.txt'")
            return video_link
        else:
            print("⚠️ Aucun lien vidéo détecté dans le presse-papier")
            print("💡 Assurez-vous d'avoir fait clic droit → 'Copier l'adresse de la vidéo'")
    except:
        print("⚠️ Impossible de lire le presse-papier")
    
    return None

def automated_with_user_help():
    """Version semi-automatique avec aide utilisateur"""
    print("\n🤖 Mode Semi-Automatique")
    print("=" * 30)
    print("Je vais ouvrir un navigateur contrôlé, mais VOUS devrez:")
    print("• Passer la vérification Cloudflare manuellement")
    print("• Me dire quand c'est fait")
    print()
    
    choice = input("Voulez-vous essayer le mode semi-automatique? (o/n): ").lower().strip()
    
    if choice not in ['o', 'oui', 'y', 'yes']:
        return None
    
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    # Configuration du navigateur visible
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Script anti-détection
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 Ouverture du navigateur contrôlé...")
        driver.get(url)
        
        print("⏳ ATTENDEZ que Cloudflare vous laisse passer...")
        print("   Vous devriez voir 'Un instant...' puis la page du film")
        print()
        input("Appuyez sur Entrée quand la page du film est chargée...")
        
        # Maintenant chercher UQload
        print("🔍 Recherche automatique du bouton UQload...")
        
        # Chercher tous les éléments cliquables
        clickable_elements = driver.find_elements(By.CSS_SELECTOR, "a, button, [onclick], [data-server], .server-item")
        
        uqload_candidates = []
        for element in clickable_elements:
            try:
                text = element.text.lower()
                href = element.get_attribute('href') or ''
                onclick = element.get_attribute('onclick') or ''
                
                if 'uqload' in text or 'uqload' in href.lower() or 'uqload' in onclick.lower():
                    uqload_candidates.append(element)
            except:
                continue
        
        if uqload_candidates:
            print(f"✅ {len(uqload_candidates)} bouton(s) UQload trouvé(s)!")
            
            for i, candidate in enumerate(uqload_candidates):
                try:
                    print(f"🖱️ Tentative de clic sur le bouton UQload #{i+1}...")
                    
                    # Scroll vers l'élément
                    driver.execute_script("arguments[0].scrollIntoView(true);", candidate)
                    time.sleep(2)
                    
                    # Cliquer
                    candidate.click()
                    time.sleep(5)
                    
                    print("✅ Clic effectué!")
                    print("👀 Regardez le navigateur - un lecteur UQload devrait apparaître")
                    print("▶️ Cliquez MANUELLEMENT sur le bouton PLAY bleu")
                    print()
                    
                    input("Appuyez sur Entrée après avoir cliqué sur PLAY...")
                    
                    # Chercher les liens vidéo
                    video_links = extract_video_from_page(driver)
                    
                    if video_links:
                        print("🎉 LIEN(S) VIDÉO TROUVÉ(S)!")
                        for link in video_links:
                            print(f"🎯 {link}")
                        
                        # Sauvegarder
                        with open('video_links_semi_auto.txt', 'w', encoding='utf-8') as f:
                            f.write(f"URL du film: {url}\n")
                            for link in video_links:
                                f.write(f"Lien vidéo: {link}\n")
                            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        
                        print("💾 Liens sauvegardés dans 'video_links_semi_auto.txt'")
                        return video_links[0]
                    
                    break
                    
                except Exception as e:
                    print(f"⚠️ Erreur avec le bouton #{i+1}: {e}")
                    continue
        else:
            print("❌ Aucun bouton UQload trouvé automatiquement")
            print("👀 Regardez le navigateur et cliquez MANUELLEMENT sur UQload")
            input("Appuyez sur Entrée après avoir cliqué sur UQload...")
            
            print("▶️ Maintenant cliquez sur le bouton PLAY bleu")
            input("Appuyez sur Entrée après avoir cliqué sur PLAY...")
            
            # Chercher les liens vidéo
            video_links = extract_video_from_page(driver)
            
            if video_links:
                print("🎉 LIEN(S) VIDÉO TROUVÉ(S)!")
                for link in video_links:
                    print(f"🎯 {link}")
                return video_links[0]
        
        input("Appuyez sur Entrée pour fermer le navigateur...")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    finally:
        if 'driver' in locals():
            driver.quit()
    
    return None

def extract_video_from_page(driver):
    """Extrait les liens vidéo de la page actuelle"""
    video_links = []
    
    try:
        # Méthode 1: Éléments video
        videos = driver.find_elements(By.TAG_NAME, "video")
        for video in videos:
            src = video.get_attribute('src')
            if src and src.startswith('http'):
                video_links.append(src)
        
        # Méthode 2: Sources dans les vidéos
        sources = driver.find_elements(By.TAG_NAME, "source")
        for source in sources:
            src = source.get_attribute('src')
            if src and src.startswith('http'):
                video_links.append(src)
        
        # Méthode 3: Analyse du code source
        import re
        page_source = driver.page_source
        
        patterns = [
            r'https?://[^"\s]+\.mp4[^"\s]*',
            r'https?://[^"\s]+\.m3u8[^"\s]*',
            r'"file":\s*"([^"]+)"',
            r'"src":\s*"([^"]+\.(?:mp4|m3u8))"'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                if match and match.startswith('http') and match not in video_links:
                    video_links.append(match)
    
    except Exception as e:
        print(f"⚠️ Erreur extraction: {e}")
    
    return list(set(video_links))

def main():
    """Fonction principale"""
    print("🎬 Extracteur UQload - Guide Manuel")
    print("=" * 50)
    print()
    print("Choisissez votre méthode:")
    print("1. 📋 Guide manuel complet (recommandé)")
    print("2. 🤖 Mode semi-automatique")
    print("3. ❌ Annuler")
    print()
    
    try:
        choice = input("Votre choix (1-3): ").strip()
        
        if choice == '1':
            video_link = open_browser_with_instructions()
            if video_link:
                print(f"\n🎯 LIEN VIDÉO FINAL: {video_link}")
        
        elif choice == '2':
            video_link = automated_with_user_help()
            if video_link:
                print(f"\n🎯 LIEN VIDÉO FINAL: {video_link}")
        
        else:
            print("❌ Annulé")
    
    except KeyboardInterrupt:
        print("\n❌ Annulé par l'utilisateur")

if __name__ == "__main__":
    try:
        import pyperclip
    except ImportError:
        print("⚠️ Installation de pyperclip pour le presse-papier...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'pyperclip'])
        import pyperclip
    
    main()
