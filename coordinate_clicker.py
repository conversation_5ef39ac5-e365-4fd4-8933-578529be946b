#!/usr/bin/env python3
"""
CLICKER PAR COORDONNÉES - CASE À COCHER CLOUDFLARE
Clique directement aux coordonnées fixes de la case à cocher
BEAUCOUP PLUS SIMPLE ET EFFICACE !
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import random

class CoordinateClicker:
    """Clique aux coordonnées exactes de la case à cocher"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        
        # Coordonnées communes pour les cases à cocher Cloudflare
        self.checkbox_coordinates = [
            # Format: (x, y, description)
            (150, 300, "Position standard Cloudflare"),
            (100, 250, "Position alternative 1"),
            (200, 350, "Position alternative 2"),
            (120, 280, "Position mobile/responsive"),
            (180, 320, "Position iframe centrée"),
            (160, 290, "Position décalée"),
            (140, 310, "Position variante"),
            (170, 270, "Position haute"),
            (130, 330, "Position basse")
        ]
    
    def setup_simple_driver(self):
        """Configure un driver simple et rapide"""
        print("🚀 Configuration du navigateur simple...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        # Options basiques
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        
        # User agent simple
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Script anti-détection minimal
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Taille de fenêtre fixe pour coordonnées précises
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def click_at_coordinates(self, x, y, description=""):
        """Clique aux coordonnées exactes"""
        try:
            print(f"🖱️ Clic aux coordonnées ({x}, {y}) - {description}")
            
            # Méthode 1: ActionChains avec coordonnées absolues
            actions = ActionChains(self.driver)
            
            # Déplacer la souris aux coordonnées
            actions.move_by_offset(x, y)
            time.sleep(random.uniform(0.5, 1.0))
            
            # Cliquer
            actions.click()
            actions.perform()
            
            # Reset de la position de la souris
            actions.move_by_offset(-x, -y)
            actions.perform()
            
            print(f"   ✅ Clic effectué à ({x}, {y})")
            time.sleep(random.uniform(2, 4))
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur clic coordonnées: {e}")
            
            # Méthode 2: JavaScript avec coordonnées
            try:
                print(f"   🔄 Tentative JavaScript...")
                self.driver.execute_script(f"""
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    document.elementFromPoint({x}, {y}).dispatchEvent(event);
                """)
                
                time.sleep(random.uniform(2, 4))
                print(f"   ✅ Clic JavaScript à ({x}, {y})")
                return True
                
            except Exception as e2:
                print(f"   ❌ Erreur JavaScript: {e2}")
                return False
    
    def try_all_coordinates(self):
        """Essaie toutes les coordonnées possibles"""
        print("🎯 Test de toutes les coordonnées de cases à cocher...")
        
        for i, (x, y, description) in enumerate(self.checkbox_coordinates, 1):
            print(f"\n📍 Tentative {i}/{len(self.checkbox_coordinates)}")
            
            if self.click_at_coordinates(x, y, description):
                # Vérifier si le clic a fonctionné
                time.sleep(3)
                
                if self.check_if_passed():
                    print(f"🎉 SUCCÈS avec les coordonnées ({x}, {y}) !")
                    return True
            
            # Attendre avant la prochaine tentative
            time.sleep(2)
        
        print("❌ Aucune coordonnée n'a fonctionné")
        return False
    
    def check_if_passed(self):
        """Vérifie si Cloudflare a été passé"""
        try:
            current_title = self.driver.title.lower()
            
            # Si le titre ne contient plus les mots Cloudflare
            if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                # Vérifier le contenu de la page
                try:
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    if body and len(body.text) > 1000:  # Page avec contenu
                        return True
                except:
                    pass
            
            return False
            
        except:
            return False
    
    def smart_coordinate_detection(self):
        """Détection intelligente des coordonnées de la case"""
        print("🧠 Détection intelligente des coordonnées...")
        
        try:
            # Prendre une capture d'écran pour analyse
            screenshot = self.driver.get_screenshot_as_png()
            
            # Chercher les éléments qui pourraient être des cases à cocher
            potential_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                "input, button, div, span, iframe")
            
            found_coordinates = []
            
            for element in potential_elements:
                try:
                    if element.is_displayed():
                        # Obtenir la position de l'élément
                        location = element.location
                        size = element.size
                        
                        # Calculer le centre de l'élément
                        center_x = location['x'] + size['width'] // 2
                        center_y = location['y'] + size['height'] // 2
                        
                        # Vérifier si c'est dans une zone raisonnable pour une case à cocher
                        if (50 <= center_x <= 400 and 200 <= center_y <= 500 and 
                            10 <= size['width'] <= 100 and 10 <= size['height'] <= 100):
                            
                            found_coordinates.append((center_x, center_y, f"Élément détecté {element.tag_name}"))
                            print(f"   📍 Coordonnées détectées: ({center_x}, {center_y})")
                
                except:
                    continue
            
            return found_coordinates
            
        except Exception as e:
            print(f"   ⚠️ Erreur détection: {e}")
            return []
    
    def bypass_cloudflare_with_coordinates(self, url):
        """Contourne Cloudflare en cliquant aux coordonnées"""
        print("🎯 CONTOURNEMENT CLOUDFLARE PAR COORDONNÉES")
        print("=" * 60)
        print(f"🔗 URL: {url}")
        print("💡 Stratégie: Clic direct aux coordonnées fixes")
        
        try:
            if not self.setup_simple_driver():
                return False
            
            # Charger la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            
            # Attendre le chargement
            time.sleep(5)
            
            # Vérifier si on a déjà accès
            if self.check_if_passed():
                print("✅ Accès direct - pas de Cloudflare!")
                return True
            
            # Méthode 1: Coordonnées prédéfinies
            print("\n🎯 Méthode 1: Coordonnées prédéfinies")
            if self.try_all_coordinates():
                return True
            
            # Méthode 2: Détection intelligente
            print("\n🧠 Méthode 2: Détection intelligente")
            detected_coords = self.smart_coordinate_detection()
            
            if detected_coords:
                for x, y, description in detected_coords:
                    if self.click_at_coordinates(x, y, description):
                        time.sleep(3)
                        if self.check_if_passed():
                            print(f"🎉 SUCCÈS avec coordonnées détectées ({x}, {y}) !")
                            return True
            
            # Méthode 3: Balayage systématique
            print("\n🔍 Méthode 3: Balayage systématique")
            return self.systematic_sweep()
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
        
        finally:
            if self.driver:
                print("\n⏳ Navigateur reste ouvert pour inspection...")
                input("Appuyez sur Entrée pour fermer...")
                self.driver.quit()
    
    def systematic_sweep(self):
        """Balayage systématique de la zone probable"""
        print("🔍 Balayage systématique de la zone Cloudflare...")
        
        # Zone probable pour la case à cocher (gauche de l'écran, milieu vertical)
        start_x, end_x = 50, 300
        start_y, end_y = 200, 400
        step = 30  # Pas de 30 pixels
        
        for y in range(start_y, end_y, step):
            for x in range(start_x, end_x, step):
                print(f"   🔍 Test ({x}, {y})")
                
                if self.click_at_coordinates(x, y, f"Balayage systématique"):
                    time.sleep(2)
                    
                    if self.check_if_passed():
                        print(f"🎉 SUCCÈS avec balayage à ({x}, {y}) !")
                        return True
                
                # Petit délai entre les clics
                time.sleep(0.5)
        
        print("❌ Balayage systématique échoué")
        return False

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎯 CLICKER PAR COORDONNÉES - CLOUDFLARE")
    print("=" * 60)
    print("💡 IDÉE GÉNIALE: Cliquer directement aux coordonnées!")
    print("🎯 Plus besoin de chercher - on clique où on sait que c'est!")
    print()
    
    clicker = CoordinateClicker(show_browser=True)
    
    if clicker.bypass_cloudflare_with_coordinates(url):
        print("\n🏆 CLOUDFLARE CONTOURNÉ PAR COORDONNÉES!")
        print("🎬 Vous pouvez maintenant extraire UQload!")
        
        # Lancer l'extracteur UQload après succès
        choice = input("\nLancer l'extracteur UQload maintenant? (o/n): ")
        if choice.lower().strip() in ['o', 'oui', 'y', 'yes']:
            print("🚀 Lancement de l'extracteur UQload...")
            # Ici on pourrait lancer l'extracteur UQload
    else:
        print("\n❌ Coordonnées non trouvées")
        print("💡 Suggestions:")
        print("   • Ajustez les coordonnées dans le script")
        print("   • Vérifiez la taille de la fenêtre")
        print("   • Essayez le guide manuel")

if __name__ == "__main__":
    main()
