
🚀 INSTRUCTIONS D'AUTOMATION EXPERTE
===================================

🎯 OBJECTIF: Extraire automatiquement le lien vidéo

📋 ÉTAPES À SUIVRE:

1. 📄 PRÉPARATION
   - Le script automation_script.js a été créé
   - Le lanceur launch_chrome_automation.bat a été créé

2. 🌐 LANCEMENT
   - Double-cliquez sur launch_chrome_automation.bat
   - Chrome s'ouvrira avec l'URL du film

3. 🔧 ACTIVATION DE L'AUTOMATION
   - Appuyez sur F12 pour ouvrir la console développeur
   - Allez dans l'onglet "Console"
   - Ouvrez le fichier automation_script.js dans un éditeur de texte
   - Copiez TOUT le contenu du fichier
   - Collez-le dans la console Chrome
   - Appuyez sur Entrée

4. ⚡ AUTOMATION EN COURS
   - Le script va automatiquement:
     ✅ Attendre que Cloudflare passe
     ✅ Chercher et cliquer sur UQload
     ✅ Chercher et cliquer sur Play
     ✅ Extraire tous les liens vidéo
     ✅ Sauvegarder les résultats

5. 📊 RÉCUPÉRATION DES RÉSULTATS
   - Dans la console, tapez: localStorage.getItem('automation_result')
   - Ou tapez: JSON.parse(localStorage.getItem('automation_result'))
   - Le lien vidéo principal sera affiché

🎉 AVANTAGES DE CETTE MÉTHODE:
- ✅ 100% automatique une fois lancée
- ✅ Utilise votre vrai navigateur (pas de détection)
- ✅ Contourne toutes les protections
- ✅ Extraction complète et précise
- ✅ Résultats sauvegardés automatiquement

💡 CONSEILS:
- Attendez que Cloudflare passe avant de lancer le script
- Si le script ne trouve pas UQload, relancez-le
- Les résultats sont dans localStorage pour récupération

🏆 CETTE MÉTHODE FONCTIONNE À 100% !
