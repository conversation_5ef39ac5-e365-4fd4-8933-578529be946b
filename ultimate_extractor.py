#!/usr/bin/env python3
"""
Ultimate French Stream Movie Extractor
Combines all extraction methods with intelligent fallbacks
"""

import sys
import json
import time
import subprocess

def check_dependencies():
    """Check and report on available dependencies"""
    deps = {
        'selenium': False,
        'requests': False,
        'beautifulsoup4': False,
        'webdriver_manager': False
    }
    
    try:
        import selenium
        deps['selenium'] = True
    except ImportError:
        pass
    
    try:
        import requests
        deps['requests'] = True
    except ImportError:
        pass
    
    try:
        import bs4
        deps['beautifulsoup4'] = True
    except ImportError:
        pass
    
    try:
        import webdriver_manager
        deps['webdriver_manager'] = True
    except ImportError:
        pass
    
    return deps

def install_dependencies():
    """Install missing dependencies"""
    print("🔧 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", 
                             "selenium", "beautifulsoup4", "requests", 
                             "webdriver-manager", "fake-useragent"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def extract_with_selenium(url):
    """Extract using Selenium (most effective for JS sites)"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print("🤖 Using Selenium WebDriver...")
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute stealth script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        try:
            print(f"   📡 Loading: {url}")
            driver.get(url)
            
            # Wait for page load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Additional wait for dynamic content
            time.sleep(5)
            
            # Extract title
            title = "Unknown Title"
            try:
                title_element = driver.find_element(By.TAG_NAME, "h1")
                title = title_element.text.strip()
            except:
                title = driver.title
            
            # Extract all links
            links = []
            link_elements = driver.find_elements(By.TAG_NAME, "a")
            
            for element in link_elements:
                try:
                    href = element.get_attribute("href")
                    if href and any(service in href.lower() for service in 
                                  ['mega.nz', '1fichier', 'uptobox', 'mediafire', 'rapidgator']):
                        links.append(href)
                except:
                    continue
            
            # Extract iframes
            iframes = []
            iframe_elements = driver.find_elements(By.TAG_NAME, "iframe")
            
            for element in iframe_elements:
                try:
                    src = element.get_attribute("src")
                    if src and any(keyword in src.lower() for keyword in ['player', 'stream', 'embed']):
                        iframes.append(src)
                except:
                    continue
            
            # Get page source for additional extraction
            page_source = driver.page_source
            
            result = {
                'title': title,
                'url': url,
                'download_links': list(set(links)),
                'streaming_links': list(set(iframes)),
                'method': 'selenium',
                'success': True
            }
            
            print(f"   ✅ Selenium extraction completed")
            print(f"   📥 Found {len(links)} download links")
            print(f"   📺 Found {len(iframes)} streaming links")
            
            return result
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"   ❌ Selenium extraction failed: {e}")
        return None

def extract_with_requests(url):
    """Extract using requests + BeautifulSoup"""
    try:
        import requests
        from bs4 import BeautifulSoup
        from fake_useragent import UserAgent
        
        print("🌐 Using Requests + BeautifulSoup...")
        
        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract title
        title = "Unknown Title"
        title_tag = soup.find('h1') or soup.find('title')
        if title_tag:
            title = title_tag.get_text(strip=True)
        
        # Extract download links
        links = []
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            if any(service in href.lower() for service in 
                  ['mega.nz', '1fichier', 'uptobox', 'mediafire', 'rapidgator']):
                links.append(href)
        
        # Extract streaming links
        iframes = []
        for iframe in soup.find_all('iframe', src=True):
            src = iframe['src']
            if any(keyword in src.lower() for keyword in ['player', 'stream', 'embed']):
                iframes.append(src)
        
        result = {
            'title': title,
            'url': url,
            'download_links': list(set(links)),
            'streaming_links': list(set(iframes)),
            'method': 'requests',
            'success': True
        }
        
        print(f"   ✅ Requests extraction completed")
        print(f"   📥 Found {len(links)} download links")
        print(f"   📺 Found {len(iframes)} streaming links")
        
        return result
        
    except Exception as e:
        print(f"   ❌ Requests extraction failed: {e}")
        return None

def manual_extraction_guide(url):
    """Provide manual extraction instructions"""
    print("\n🔧 MANUAL EXTRACTION GUIDE")
    print("=" * 50)
    print("Since automated extraction failed, here's how to extract manually:")
    print()
    print("1. 🌐 Open this URL in your browser:")
    print(f"   {url}")
    print()
    print("2. ⏳ Wait for the page to fully load (including JavaScript)")
    print()
    print("3. 🔍 Look for download buttons or links containing:")
    print("   • Mega.nz")
    print("   • 1fichier.com") 
    print("   • Uptobox.com")
    print("   • MediaFire.com")
    print("   • RapidGator.net")
    print()
    print("4. 📋 Right-click on download links and copy the URL")
    print()
    print("5. 🔎 Alternative: View page source (Ctrl+U) and search for:")
    print("   • 'mega.nz'")
    print("   • '1fichier'")
    print("   • 'uptobox'")
    print("   • 'mediafire'")
    print()
    print("6. 📺 For streaming links, look for <iframe> tags with 'player' or 'embed'")

def main():
    """Main extraction function"""
    print("🎬 Ultimate French Stream Movie Extractor")
    print("=" * 60)
    
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    # Check dependencies
    deps = check_dependencies()
    missing_deps = [dep for dep, available in deps.items() if not available]
    
    if missing_deps:
        print(f"⚠️  Missing dependencies: {', '.join(missing_deps)}")
        print("🔧 Would you like to install them? (y/n): ", end="")
        
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes']:
                if install_dependencies():
                    print("🔄 Restarting extraction with new dependencies...")
                    time.sleep(2)
                    # Re-check dependencies
                    deps = check_dependencies()
        except KeyboardInterrupt:
            print("\n❌ Installation cancelled")
    
    # Try extraction methods in order of effectiveness
    result = None
    
    # Method 1: Selenium (most effective)
    if deps['selenium'] and deps['webdriver_manager']:
        result = extract_with_selenium(url)
    
    # Method 2: Requests + BeautifulSoup
    if not result and deps['requests'] and deps['beautifulsoup4']:
        result = extract_with_requests(url)
    
    # Display results
    if result and result['success']:
        print(f"\n{'='*60}")
        print(f"🎬 EXTRACTION SUCCESSFUL!")
        print(f"{'='*60}")
        print(f"📽️  Title: {result['title']}")
        print(f"🔗 URL: {result['url']}")
        print(f"🛠️  Method: {result['method']}")
        
        if result['download_links']:
            print(f"\n📥 DOWNLOAD LINKS ({len(result['download_links'])} found):")
            for i, link in enumerate(result['download_links'], 1):
                print(f"  {i}. {link}")
        
        if result['streaming_links']:
            print(f"\n📺 STREAMING LINKS ({len(result['streaming_links'])} found):")
            for i, link in enumerate(result['streaming_links'], 1):
                print(f"  {i}. {link}")
        
        # Save results
        with open("ultimate_extraction_results.json", "w", encoding="utf-8") as f:
            json.dump([result], f, indent=2, ensure_ascii=False)
        print(f"\n💾 Results saved to: ultimate_extraction_results.json")
        
        # Return primary download link
        if result['download_links']:
            print(f"\n🎯 PRIMARY DOWNLOAD LINK:")
            print(f"   {result['download_links'][0]}")
            return result['download_links'][0]
    
    else:
        print(f"\n❌ All automated extraction methods failed")
        manual_extraction_guide(url)
        
        print(f"\n💡 Additional suggestions:")
        print(f"   • Try using a VPN")
        print(f"   • Use a different IP address")
        print(f"   • Wait and try again later")
        print(f"   • Check if the movie URL is still valid")
    
    return None

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Extraction cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("💡 Try running: python install_dependencies.py")
