#!/usr/bin/env python3
"""
Example usage of the French Stream Movie Extractor
"""

from french_stream_extractor import FrenchStreamExtractor, MovieInfo

def test_single_movie():
    """Test extracting a single movie"""
    print("Testing single movie extraction...")
    
    # Initialize the extractor
    extractor = FrenchStreamExtractor(headless=True, timeout=30)
    
    # The URL provided by the user
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    try:
        # Extract movie information
        movie_info = extractor.extract_movie_info(url)
        
        if movie_info:
            print(f"\n✅ Successfully extracted movie: {movie_info.title}")
            print(f"📅 Year: {movie_info.year or 'Unknown'}")
            print(f"🎬 Quality: {movie_info.quality or 'Unknown'}")
            print(f"📥 Download links found: {len(movie_info.download_links)}")
            print(f"📺 Streaming links found: {len(movie_info.streaming_links)}")
            
            if movie_info.download_links:
                print("\n📥 Download Links:")
                for i, link in enumerate(movie_info.download_links[:5], 1):
                    print(f"  {i}. {link}")
                if len(movie_info.download_links) > 5:
                    print(f"  ... and {len(movie_info.download_links) - 5} more")
            
            if movie_info.streaming_links:
                print("\n📺 Streaming Links:")
                for i, link in enumerate(movie_info.streaming_links[:3], 1):
                    print(f"  {i}. {link}")
                if len(movie_info.streaming_links) > 3:
                    print(f"  ... and {len(movie_info.streaming_links) - 3} more")
            
            return movie_info
        else:
            print("❌ Failed to extract movie information")
            return None
            
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return None

def test_batch_processing():
    """Test batch processing with multiple URLs"""
    print("\nTesting batch processing...")
    
    # Create a sample URLs file
    sample_urls = [
        "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html",
        # Add more URLs here for testing
    ]
    
    with open("sample_urls.txt", "w", encoding="utf-8") as f:
        for url in sample_urls:
            f.write(url + "\n")
    
    extractor = FrenchStreamExtractor(headless=True, timeout=30)
    
    try:
        results = extractor.extract_multiple_movies(sample_urls)
        
        if results:
            print(f"✅ Successfully processed {len(results)} movies")
            extractor.save_results_to_json(results, "test_results.json")
            print("📄 Results saved to test_results.json")
        else:
            print("❌ No movies were successfully processed")
            
        return results
        
    except Exception as e:
        print(f"❌ Error during batch processing: {e}")
        return []

def test_discovery():
    """Test movie discovery from catalog"""
    print("\nTesting movie discovery...")
    
    extractor = FrenchStreamExtractor(headless=True, timeout=30)
    base_url = "https://vvw.french-stream.bio"
    
    try:
        # Discover movies from the catalog
        movie_urls = extractor.discover_movie_urls(base_url, max_pages=2)
        
        if movie_urls:
            print(f"✅ Discovered {len(movie_urls)} movie URLs")
            
            # Show first few URLs
            print("\n🔍 Sample discovered URLs:")
            for i, url in enumerate(movie_urls[:5], 1):
                print(f"  {i}. {url}")
            if len(movie_urls) > 5:
                print(f"  ... and {len(movie_urls) - 5} more")
            
            return movie_urls
        else:
            print("❌ No movie URLs discovered")
            return []
            
    except Exception as e:
        print(f"❌ Error during discovery: {e}")
        return []

def main():
    """Main test function"""
    print("🎬 French Stream Movie Extractor - Test Suite")
    print("=" * 50)
    
    # Test 1: Single movie extraction
    movie_info = test_single_movie()
    
    # Test 2: Batch processing
    batch_results = test_batch_processing()
    
    # Test 3: Movie discovery
    discovered_urls = test_discovery()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Single movie: {'✅ Success' if movie_info else '❌ Failed'}")
    print(f"  Batch processing: {'✅ Success' if batch_results else '❌ Failed'}")
    print(f"  Movie discovery: {'✅ Success' if discovered_urls else '❌ Failed'}")
    
    if movie_info:
        print(f"\n🎯 Main movie extracted: {movie_info.title}")
        if movie_info.download_links:
            print(f"📥 Primary download link: {movie_info.download_links[0]}")

if __name__ == "__main__":
    main()
