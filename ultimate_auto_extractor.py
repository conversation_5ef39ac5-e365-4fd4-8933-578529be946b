#!/usr/bin/env python3
"""
ULTIMATE AUTO EXTRACTOR - 100% AUTOMATISÉ
Expert en: Automation complète, Extraction forcée, Techniques avancées
OBJECTIF: Extraire automatiquement le lien vidéo SANS intervention manuelle
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re
import requests
from urllib.parse import urljoin, urlparse
import base64

class UltimateAutoExtractor:
    """Extracteur automatique ultime - ZÉRO intervention manuelle"""

    def __init__(self):
        self.driver = None
        self.session = requests.Session()
        self.video_urls = []

    def setup_ultimate_browser(self):
        """Configuration navigateur ultime pour automation complète"""
        print("🚀 Configuration navigateur ULTIMATE AUTO...")

        chrome_options = Options()

        # Options ultra-stealth pour automation invisible
        stealth_args = [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-first-run",
            "--safebrowsing-disable-auto-update",
            "--disable-default-apps",
            "--disable-background-timer-throttling",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--mute-audio",
            "--no-default-browser-check",
            "--autoplay-policy=user-gesture-required",
            "--disable-background-mode"
        ]

        for arg in stealth_args:
            chrome_options.add_argument(arg)

        # User agent ultra-réaliste
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Prefs pour automation invisible
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.media_stream": 1,
            "profile.default_content_setting_values.geolocation": 2,
            "profile.managed_default_content_settings.media_stream": 1,
            "webrtc.ip_handling_policy": "disable_non_proxied_udp",
            "webrtc.multiple_routes_enabled": False,
            "webrtc.nonproxied_udp_enabled": False,
            "profile.default_content_setting_values.media_stream_mic": 2,
            "profile.default_content_setting_values.media_stream_camera": 2,
            "profile.default_content_setting_values.protocol_handlers": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Désactiver complètement l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Scripts stealth ultra-avancés
            stealth_script = """
            // Supprimer TOUTES les traces d'automation
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr-FR', 'fr']});
            Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
            Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
            Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
            Object.defineProperty(navigator, 'maxTouchPoints', {get: () => 0});
            Object.defineProperty(screen, 'width', {get: () => 1920});
            Object.defineProperty(screen, 'height', {get: () => 1080});
            Object.defineProperty(screen, 'availWidth', {get: () => 1920});
            Object.defineProperty(screen, 'availHeight', {get: () => 1040});
            Object.defineProperty(screen, 'colorDepth', {get: () => 24});
            Object.defineProperty(screen, 'pixelDepth', {get: () => 24});

            // Chrome properties ultra-réalistes
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                },
                loadTimes: function() {
                    return {
                        commitLoadTime: Date.now() / 1000 - Math.random(),
                        finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                        finishLoadTime: Date.now() / 1000 - Math.random(),
                        firstPaintAfterLoadTime: 0,
                        firstPaintTime: Date.now() / 1000 - Math.random(),
                        navigationType: "Other",
                        npnNegotiatedProtocol: "h2",
                        requestTime: Date.now() / 1000 - Math.random(),
                        startLoadTime: Date.now() / 1000 - Math.random(),
                        wasAlternateProtocolAvailable: false,
                        wasFetchedViaSpdy: true,
                        wasNpnNegotiated: true
                    };
                },
                csi: function() {
                    return {
                        onloadT: Date.now(),
                        pageT: Date.now() - Math.random() * 1000,
                        tran: 15
                    };
                }
            };

            // Masquer automation
            delete navigator.__proto__.webdriver;
            delete window.navigator.webdriver;

            // Events réalistes
            ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup', 'click'].forEach(event => {
                document.addEventListener(event, () => {}, true);
            });

            // Performance API réaliste
            if (window.performance && window.performance.getEntriesByType) {
                const originalGetEntriesByType = window.performance.getEntriesByType;
                window.performance.getEntriesByType = function(type) {
                    const entries = originalGetEntriesByType.call(this, type);
                    if (type === 'navigation') {
                        entries.forEach(entry => {
                            entry.type = 'navigate';
                        });
                    }
                    return entries;
                };
            }
            """

            self.driver.execute_script(stealth_script)

            # Taille réaliste avec variation
            self.driver.set_window_size(1366, 768)

            print("   ✅ Navigateur ultimate configuré")
            return True

        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            return False

    def ultimate_cloudflare_bypass(self, url):
        """Contournement Cloudflare ultime avec techniques avancées"""
        print("🛡️ Contournement Cloudflare ULTIMATE...")

        try:
            # Navigation avec headers réalistes
            self.driver.get(url)

            # Attente intelligente avec vérifications multiples
            max_wait = 60
            start_time = time.time()

            while time.time() - start_time < max_wait:
                try:
                    # Vérification 1: Titre de la page
                    current_title = self.driver.title.lower()
                    if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking", "please wait"]):
                        # Vérification 2: Contenu de la page
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:
                            # Vérification 3: Présence d'éléments de contenu
                            content_indicators = self.driver.execute_script("""
                                var indicators = 0;
                                if (document.querySelectorAll('a').length > 10) indicators++;
                                if (document.querySelectorAll('div').length > 20) indicators++;
                                if (document.querySelectorAll('img').length > 5) indicators++;
                                if (document.body.innerText.length > 2000) indicators++;
                                return indicators;
                            """)

                            if content_indicators >= 2:
                                print("   ✅ Cloudflare contourné avec succès!")
                                return True

                    # Simulation d'activité humaine pendant l'attente
                    if random.random() < 0.3:
                        self._simulate_human_activity()

                except Exception as e:
                    print(f"   ⚠️ Erreur vérification: {e}")

                time.sleep(2)

            print("   ⚠️ Timeout Cloudflare - Continuons avec extraction forcée")
            return True

        except Exception as e:
            print(f"   ❌ Erreur contournement: {e}")
            return False

    def _simulate_human_activity(self):
        """Simulation d'activité humaine ultra-réaliste"""
        try:
            actions = ActionChains(self.driver)

            # Mouvements de souris naturels
            for _ in range(random.randint(1, 3)):
                x = random.randint(-100, 100)
                y = random.randint(-100, 100)
                actions.move_by_offset(x, y)
                time.sleep(random.uniform(0.1, 0.3))

            actions.perform()

            # Scroll aléatoire
            if random.random() < 0.5:
                scroll_amount = random.randint(100, 500)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.5, 1.0))
                self.driver.execute_script(f"window.scrollBy(0, -{scroll_amount});")

        except:
            pass

    def ultimate_uqload_finder(self):
        """Recherche UQload ultime avec toutes les techniques"""
        print("🔍 Recherche UQload ULTIMATE...")

        try:
            # Attendre le chargement complet
            time.sleep(5)

            # Méthode 1: Recherche exhaustive par sélecteurs
            uqload_elements = self.driver.execute_script("""
                var elements = [];

                // Sélecteurs ultra-complets
                var selectors = [
                    'a[href*="uqload" i]',
                    'button[onclick*="uqload" i]',
                    '[data-server*="uqload" i]',
                    '*[class*="uqload" i]',
                    '*[id*="uqload" i]',
                    '[data-link*="uqload" i]',
                    '[data-url*="uqload" i]',
                    '[data-src*="uqload" i]',
                    '[title*="uqload" i]',
                    '[alt*="uqload" i]',
                    '[data-title*="uqload" i]',
                    'a[href*="uq" i]',
                    'button[onclick*="uq" i]'
                ];

                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            var rect = element.getBoundingClientRect();
                            if (rect.width > 5 && rect.height > 5) {
                                elements.push({
                                    method: 'selector',
                                    selector: selector,
                                    text: (element.textContent || element.innerText || element.title || element.alt || '').substring(0, 100),
                                    href: element.href || element.getAttribute('data-link') || element.getAttribute('data-url') || '',
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    },
                                    element: element
                                });
                            }
                        });
                    } catch(e) {}
                });

                // Méthode 2: Recherche par texte dans tout le DOM
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                var textNode;
                while (textNode = walker.nextNode()) {
                    var text = textNode.textContent.toLowerCase();
                    if (text.includes('uqload') || text.includes('uq load') ||
                        (text.includes('uq') && text.length < 10)) {
                        var parent = textNode.parentElement;
                        if (parent) {
                            var rect = parent.getBoundingClientRect();
                            if (rect.width > 10 && rect.height > 10) {
                                elements.push({
                                    method: 'text',
                                    text: textNode.textContent.substring(0, 100),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    },
                                    element: parent
                                });
                            }
                        }
                    }
                }

                // Méthode 3: Recherche par attributs cachés
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    var attrs = element.attributes;

                    for (var j = 0; j < attrs.length; j++) {
                        var attr = attrs[j];
                        if (attr.value && attr.value.toLowerCase().includes('uqload')) {
                            var rect = element.getBoundingClientRect();
                            if (rect.width > 5 && rect.height > 5) {
                                elements.push({
                                    method: 'attribute',
                                    attribute: attr.name,
                                    value: attr.value.substring(0, 100),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    },
                                    element: element
                                });
                            }
                        }
                    }
                }

                return elements;
            """)

            print(f"   📍 {len(uqload_elements)} éléments UQload trouvés")

            # Afficher les éléments trouvés
            for i, element in enumerate(uqload_elements[:10]):
                method = element['method']
                text = element.get('text', element.get('value', '')).strip()
                pos = element['position']
                print(f"   {i+1}. [{method}] '{text[:30]}' à ({pos['x']}, {pos['y']})")

            return uqload_elements

        except Exception as e:
            print(f"   ❌ Erreur recherche UQload: {e}")
            return []

    def ultimate_click_uqload(self, uqload_elements):
        """Clic UQload ultime avec techniques multiples"""
        if not uqload_elements:
            print("❌ Aucun élément UQload trouvé")
            return False

        print("🖱️ Clic UQload ULTIMATE...")

        # Essayer tous les éléments trouvés
        for i, element in enumerate(uqload_elements[:5]):
            try:
                method = element['method']
                x, y = element['position']['x'], element['position']['y']
                text = element.get('text', element.get('value', '')).strip()

                print(f"   🎯 Tentative {i+1}: [{method}] '{text[:20]}' à ({x}, {y})")

                # Méthode 1: Clic JavaScript direct
                success = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        // Essayer plusieurs types de clics
                        try {{
                            element.click();
                            return true;
                        }} catch(e1) {{
                            try {{
                                var event = new MouseEvent('click', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: {x},
                                    clientY: {y}
                                }});
                                element.dispatchEvent(event);
                                return true;
                            }} catch(e2) {{
                                try {{
                                    if (element.href) {{
                                        window.location.href = element.href;
                                        return true;
                                    }}
                                }} catch(e3) {{
                                    return false;
                                }}
                            }}
                        }}
                    }}
                    return false;
                """)

                if success:
                    print(f"   ✅ Clic UQload réussi!")
                    time.sleep(5)
                    return True

                # Méthode 2: ActionChains
                try:
                    actions = ActionChains(self.driver)
                    actions.move_by_offset(x - 400, y - 300)
                    actions.click()
                    actions.perform()

                    time.sleep(3)

                    # Vérifier si on a changé de page
                    new_url = self.driver.current_url
                    if 'uqload' in new_url.lower():
                        print(f"   ✅ Navigation UQload réussie!")
                        return True

                except Exception as e:
                    print(f"   ⚠️ ActionChains échoué: {e}")

                # Méthode 3: Navigation directe si href disponible
                href = element.get('href', '')
                if href and 'uqload' in href.lower():
                    try:
                        self.driver.get(href)
                        print(f"   ✅ Navigation directe UQload réussie!")
                        time.sleep(5)
                        return True
                    except Exception as e:
                        print(f"   ⚠️ Navigation directe échouée: {e}")

            except Exception as e:
                print(f"   ❌ Erreur tentative {i+1}: {e}")
                continue

        print("   ❌ Toutes les tentatives UQload ont échoué")
        return False

    def ultimate_play_finder_and_clicker(self):
        """Recherche et clic play ultime automatique"""
        print("▶️ Recherche et clic PLAY ULTIMATE...")

        try:
            # Attendre le chargement du lecteur
            time.sleep(8)

            # Recherche exhaustive de boutons play
            play_elements = self.driver.execute_script("""
                var elements = [];

                // Sélecteurs play ultra-complets
                var selectors = [
                    '.play-button',
                    '.video-play-button',
                    '[class*="play"]',
                    'button[class*="play"]',
                    '[onclick*="play"]',
                    '.vjs-big-play-button',
                    '[title*="play" i]',
                    '[alt*="play" i]',
                    '.play',
                    '#play',
                    '[data-action="play"]',
                    '.player-play',
                    '.btn-play',
                    '.video-overlay',
                    '.play-overlay',
                    '[role="button"]'
                ];

                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            var rect = element.getBoundingClientRect();
                            if (rect.width > 20 && rect.height > 20) {
                                elements.push({
                                    method: 'selector',
                                    selector: selector,
                                    text: (element.textContent || element.title || element.alt || '').substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });

                // Recherche par zones probables (centre écran)
                var centerX = Math.round(window.innerWidth / 2);
                var centerY = Math.round(window.innerHeight / 2);

                // Tester plusieurs positions autour du centre
                var positions = [
                    {x: centerX, y: centerY},
                    {x: centerX - 50, y: centerY - 50},
                    {x: centerX + 50, y: centerY + 50},
                    {x: centerX, y: centerY - 100},
                    {x: centerX, y: centerY + 100}
                ];

                positions.forEach(function(pos) {
                    elements.push({
                        method: 'center_position',
                        text: 'Position centrale calculée',
                        position: pos
                    });
                });

                return elements;
            """)

            print(f"   ▶️ {len(play_elements)} éléments play trouvés")

            # Essayer tous les éléments play
            for i, element in enumerate(play_elements[:10]):
                try:
                    method = element['method']
                    x, y = element['position']['x'], element['position']['y']
                    text = element.get('text', '').strip()

                    print(f"   🎯 Tentative play {i+1}: [{method}] '{text[:20]}' à ({x}, {y})")

                    # Clic multiple avec vérification
                    success = self.driver.execute_script(f"""
                        var element = document.elementFromPoint({x}, {y});
                        if (element) {{
                            // Essayer plusieurs événements
                            try {{
                                element.click();

                                // Aussi déclencher mousedown/mouseup
                                var mousedown = new MouseEvent('mousedown', {{
                                    view: window, bubbles: true, cancelable: true,
                                    clientX: {x}, clientY: {y}
                                }});
                                var mouseup = new MouseEvent('mouseup', {{
                                    view: window, bubbles: true, cancelable: true,
                                    clientX: {x}, clientY: {y}
                                }});

                                element.dispatchEvent(mousedown);
                                setTimeout(() => element.dispatchEvent(mouseup), 100);

                                return true;
                            }} catch(e) {{
                                return false;
                            }}
                        }}
                        return false;
                    """)

                    if success:
                        print(f"   ✅ Clic play effectué!")
                        time.sleep(5)

                        # Vérifier si une vidéo se charge
                        video_detected = self.driver.execute_script("""
                            var videos = document.querySelectorAll('video');
                            for (var i = 0; i < videos.length; i++) {
                                var video = videos[i];
                                if (video.src || video.currentSrc || video.querySelector('source')) {
                                    return true;
                                }
                            }
                            return false;
                        """)

                        if video_detected:
                            print(f"   🎥 Vidéo détectée après clic play!")
                            return True

                except Exception as e:
                    print(f"   ⚠️ Erreur tentative play {i+1}: {e}")
                    continue

            print("   ⚠️ Aucun clic play réussi - Extraction directe")
            return False

        except Exception as e:
            print(f"   ❌ Erreur recherche play: {e}")
            return False

    def ultimate_video_extraction(self):
        """Extraction vidéo ultime avec toutes les techniques avancées"""
        print("🎥 Extraction vidéo ULTIMATE...")

        try:
            # Attendre le chargement complet
            time.sleep(10)

            video_urls = []

            # Méthode 1: Éléments video directs
            print("   📹 Méthode 1: Éléments video directs...")
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                # Source directe
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append({
                        'url': src,
                        'method': 'video_src',
                        'quality': 'direct',
                        'type': self._get_video_type(src)
                    })
                    print(f"   📹 Video src: {src[:80]}...")

                # Sources multiples
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append({
                            'url': src,
                            'method': 'video_source',
                            'quality': source.get_attribute('label') or 'unknown',
                            'type': self._get_video_type(src)
                        })
                        print(f"   📹 Video source: {src[:80]}...")

            # Méthode 2: Analyse JavaScript avancée
            print("   🔍 Méthode 2: Analyse JavaScript avancée...")
            js_videos = self.driver.execute_script("""
                var videos = [];

                // Chercher dans toutes les variables globales
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string') {
                            if ((value.includes('.mp4') || value.includes('.m3u8') ||
                                 value.includes('.webm') || value.includes('.avi') ||
                                 value.includes('.mkv') || value.includes('video')) &&
                                value.startsWith('http')) {
                                videos.push({url: value, source: 'window_' + prop});
                            }
                        } else if (typeof value === 'object' && value !== null) {
                            // Chercher dans les objets
                            for (var subProp in value) {
                                try {
                                    var subValue = value[subProp];
                                    if (typeof subValue === 'string' &&
                                        (subValue.includes('.mp4') || subValue.includes('.m3u8') ||
                                         subValue.includes('.webm')) &&
                                        subValue.startsWith('http')) {
                                        videos.push({url: subValue, source: 'object_' + prop + '.' + subProp});
                                    }
                                } catch(e) {}
                            }
                        }
                    } catch(e) {}
                }

                // Chercher dans les attributs data-*
                var elements = document.querySelectorAll('[data-src], [data-url], [data-file], [data-video]');
                elements.forEach(function(element) {
                    ['data-src', 'data-url', 'data-file', 'data-video'].forEach(function(attr) {
                        var value = element.getAttribute(attr);
                        if (value && value.startsWith('http') &&
                            (value.includes('.mp4') || value.includes('.m3u8') || value.includes('.webm'))) {
                            videos.push({url: value, source: 'data_attribute_' + attr});
                        }
                    });
                });

                // Chercher dans les scripts
                var scripts = document.querySelectorAll('script');
                scripts.forEach(function(script, index) {
                    var content = script.innerHTML;
                    if (content) {
                        // Regex pour trouver des URLs vidéo
                        var patterns = [
                            /"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"/gi,
                            /"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"/gi,
                            /"url":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"/gi,
                            /https?:\/\/[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?/gi
                        ];

                        patterns.forEach(function(pattern) {
                            var matches = content.match(pattern);
                            if (matches) {
                                matches.forEach(function(match) {
                                    var url = match.replace(/["']/g, '').replace(/^(file|src|url):\s*/, '');
                                    if (url.startsWith('http')) {
                                        videos.push({url: url, source: 'script_' + index});
                                    }
                                });
                            }
                        });
                    }
                });

                return videos;
            """)

            for js_video in js_videos:
                if not any(v['url'] == js_video['url'] for v in video_urls):
                    video_urls.append({
                        'url': js_video['url'],
                        'method': 'javascript_' + js_video['source'],
                        'quality': 'extracted',
                        'type': self._get_video_type(js_video['url'])
                    })
                    print(f"   📹 JS extraction: {js_video['url'][:80]}...")

            # Méthode 3: Analyse du code source avec regex avancés
            print("   🔍 Méthode 3: Analyse code source regex...")
            page_source = self.driver.page_source

            advanced_patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"url":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"video":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"stream":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm|avi|mkv)(?:\?[^"\s]*)?',
                r'https?://[^"\s]*(?:video|stream|play|media)[^"\s]*\.(?:mp4|m3u8|webm)',
                r'https?://[^"\s]*uqload[^"\s]*\.(?:mp4|m3u8|webm)',
                r'src=[\'"](https?://[^"\']+\.(?:mp4|m3u8|webm))[\'"]',
                r'data-src=[\'"](https?://[^"\']+\.(?:mp4|m3u8|webm))[\'"]'
            ]

            for pattern in advanced_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and not any(v['url'] == match for v in video_urls):
                        video_urls.append({
                            'url': match,
                            'method': 'regex_source',
                            'quality': 'extracted',
                            'type': self._get_video_type(match)
                        })
                        print(f"   📹 Regex match: {match[:80]}...")

            # Méthode 4: Network monitoring (si possible)
            print("   🌐 Méthode 4: Network monitoring...")
            try:
                network_urls = self.driver.execute_script("""
                    var urls = [];
                    if (window.performance && window.performance.getEntriesByType) {
                        var entries = window.performance.getEntriesByType('resource');
                        for (var i = 0; i < entries.length; i++) {
                            var url = entries[i].name;
                            if ((url.includes('.mp4') || url.includes('.m3u8') ||
                                 url.includes('.webm') || url.includes('video') ||
                                 url.includes('stream')) && url.startsWith('http')) {
                                urls.push(url);
                            }
                        }
                    }
                    return urls;
                """)

                for network_url in network_urls:
                    if not any(v['url'] == network_url for v in video_urls):
                        video_urls.append({
                            'url': network_url,
                            'method': 'network_monitoring',
                            'quality': 'network',
                            'type': self._get_video_type(network_url)
                        })
                        print(f"   📹 Network: {network_url[:80]}...")

            except Exception as e:
                print(f"   ⚠️ Network monitoring échoué: {e}")

            # Méthode 5: Iframe analysis
            print("   🖼️ Méthode 5: Analyse iframes...")
            try:
                iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                for iframe in iframes:
                    src = iframe.get_attribute('src')
                    if src and ('video' in src.lower() or 'stream' in src.lower() or 'player' in src.lower()):
                        # Essayer d'analyser le contenu de l'iframe
                        try:
                            self.driver.switch_to.frame(iframe)
                            iframe_videos = self.driver.find_elements(By.TAG_NAME, "video")
                            for video in iframe_videos:
                                video_src = video.get_attribute('src')
                                if video_src and video_src.startswith('http'):
                                    video_urls.append({
                                        'url': video_src,
                                        'method': 'iframe_analysis',
                                        'quality': 'iframe',
                                        'type': self._get_video_type(video_src)
                                    })
                                    print(f"   📹 Iframe: {video_src[:80]}...")
                            self.driver.switch_to.default_content()
                        except:
                            self.driver.switch_to.default_content()

            except Exception as e:
                print(f"   ⚠️ Iframe analysis échoué: {e}")

            # Supprimer les doublons et trier par qualité
            unique_videos = []
            seen_urls = set()

            for video in video_urls:
                if video['url'] not in seen_urls:
                    unique_videos.append(video)
                    seen_urls.add(video['url'])

            # Trier par priorité (mp4 > m3u8 > webm)
            def video_priority(video):
                url = video['url'].lower()
                if '.mp4' in url:
                    return 1
                elif '.m3u8' in url:
                    return 2
                elif '.webm' in url:
                    return 3
                else:
                    return 4

            unique_videos.sort(key=video_priority)

            print(f"\n🎬 {len(unique_videos)} liens vidéo uniques trouvés")

            return unique_videos

        except Exception as e:
            print(f"   ❌ Erreur extraction vidéo: {e}")
            return []

    def _get_video_type(self, url):
        """Détermine le type de vidéo"""
        url_lower = url.lower()
        if '.mp4' in url_lower:
            return 'mp4'
        elif '.m3u8' in url_lower:
            return 'm3u8'
        elif '.webm' in url_lower:
            return 'webm'
        elif '.avi' in url_lower:
            return 'avi'
        elif '.mkv' in url_lower:
            return 'mkv'
        else:
            return 'unknown'

    def save_ultimate_results(self, video_urls, original_url):
        """Sauvegarde les résultats avec analyse complète"""
        if not video_urls:
            print("❌ Aucun résultat à sauvegarder")
            return None

        # Analyser les URLs pour déterminer la meilleure
        best_video = self._select_best_video(video_urls)

        result = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'original_url': original_url,
            'current_url': self.driver.current_url,
            'extraction_method': 'ultimate_auto_extractor',
            'total_videos_found': len(video_urls),
            'best_video': best_video,
            'all_videos': video_urls,
            'success': True,
            'extraction_summary': {
                'mp4_count': len([v for v in video_urls if v['type'] == 'mp4']),
                'm3u8_count': len([v for v in video_urls if v['type'] == 'm3u8']),
                'webm_count': len([v for v in video_urls if v['type'] == 'webm']),
                'methods_used': list(set([v['method'] for v in video_urls]))
            }
        }

        # Sauvegarder
        with open('ultimate_auto_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Résultats sauvegardés: ultimate_auto_results.json")

        return result

    def _select_best_video(self, video_urls):
        """Sélectionne la meilleure vidéo basée sur des critères"""
        if not video_urls:
            return None

        # Critères de priorité
        def video_score(video):
            score = 0
            url = video['url'].lower()
            method = video['method'].lower()

            # Type de fichier (MP4 prioritaire)
            if '.mp4' in url:
                score += 100
            elif '.m3u8' in url:
                score += 80
            elif '.webm' in url:
                score += 60

            # Méthode d'extraction (direct prioritaire)
            if 'video_src' in method:
                score += 50
            elif 'video_source' in method:
                score += 40
            elif 'javascript' in method:
                score += 30

            # Qualité de l'URL
            if 'hd' in url or '720' in url or '1080' in url:
                score += 20

            # Longueur de l'URL (plus courte souvent meilleure)
            score -= len(url) // 100

            return score

        # Trier par score et retourner le meilleur
        sorted_videos = sorted(video_urls, key=video_score, reverse=True)
        return sorted_videos[0]

    def display_ultimate_results(self, result):
        """Affiche les résultats de manière claire"""
        if not result:
            print("❌ Aucun résultat à afficher")
            return

        print("\n" + "="*80)
        print("🎉 EXTRACTION AUTOMATIQUE ULTIMATE RÉUSSIE!")
        print("="*80)

        best_video = result['best_video']
        print(f"🎯 MEILLEUR LIEN VIDÉO:")
        print(f"   URL: {best_video['url']}")
        print(f"   Type: {best_video['type'].upper()}")
        print(f"   Méthode: {best_video['method']}")
        print(f"   Qualité: {best_video['quality']}")

        print(f"\n📊 STATISTIQUES:")
        summary = result['extraction_summary']
        print(f"   Total vidéos trouvées: {result['total_videos_found']}")
        print(f"   MP4: {summary['mp4_count']}")
        print(f"   M3U8: {summary['m3u8_count']}")
        print(f"   WebM: {summary['webm_count']}")
        print(f"   Méthodes utilisées: {len(summary['methods_used'])}")

        if len(result['all_videos']) > 1:
            print(f"\n📹 TOUS LES LIENS TROUVÉS ({len(result['all_videos'])}):")
            for i, video in enumerate(result['all_videos'], 1):
                print(f"   {i}. [{video['type'].upper()}] {video['url']}")

        print(f"\n💾 Résultats complets: ultimate_auto_results.json")
        print("="*80)

    def run_ultimate_extraction(self, url):
        """Lance l'extraction automatique ultime complète"""
        print("🚀 ULTIMATE AUTO EXTRACTOR - 100% AUTOMATISÉ")
        print("=" * 80)
        print("👨‍💻 Expert en: Automation complète, Extraction forcée")
        print("🎯 OBJECTIF: Extraire automatiquement SANS intervention manuelle")
        print(f"🔗 URL: {url}")
        print("=" * 80)

        try:
            # Étape 1: Configuration navigateur ultimate
            if not self.setup_ultimate_browser():
                print("❌ Configuration navigateur échouée")
                return False

            # Étape 2: Contournement Cloudflare ultimate
            if not self.ultimate_cloudflare_bypass(url):
                print("❌ Contournement Cloudflare échoué")
                return False

            # Étape 3: Recherche UQload ultimate
            print("\n" + "="*50)
            uqload_elements = self.ultimate_uqload_finder()

            # Étape 4: Clic UQload ultimate
            uqload_success = False
            if uqload_elements:
                uqload_success = self.ultimate_click_uqload(uqload_elements)

            # Étape 5: Recherche et clic play ultimate
            if uqload_success:
                print("\n" + "="*50)
                self.ultimate_play_finder_and_clicker()

            # Étape 6: Extraction vidéo ultimate
            print("\n" + "="*50)
            video_urls = self.ultimate_video_extraction()

            # Étape 7: Sauvegarde et affichage
            if video_urls:
                result = self.save_ultimate_results(video_urls, url)
                self.display_ultimate_results(result)
                return True
            else:
                print("\n❌ AUCUNE VIDÉO TROUVÉE")
                print("💡 Le site peut utiliser une protection très avancée")
                return False

        except Exception as e:
            print(f"\n❌ ERREUR FATALE: {e}")
            return False

        finally:
            if self.driver:
                print(f"\n⏳ Navigateur reste ouvert pour inspection...")
                input("Appuyez sur Entrée pour fermer...")
                self.driver.quit()

import random

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"

    print("🚀 ULTIMATE AUTO EXTRACTOR")
    print("=" * 60)
    print("🎯 EXTRACTION 100% AUTOMATISÉE")
    print("🔥 AUCUNE INTERVENTION MANUELLE REQUISE")
    print("💪 TECHNIQUES EXPERTES AVANCÉES")

    extractor = UltimateAutoExtractor()
    success = extractor.run_ultimate_extraction(url)

    if success:
        print("\n🏆 MISSION AUTOMATIQUE ACCOMPLIE!")
        print("🎬 Lien vidéo extrait automatiquement!")
    else:
        print("\n❌ Extraction automatique échouée")
        print("💡 Vérifiez les résultats partiels dans ultimate_auto_results.json")

if __name__ == "__main__":
    main()