#!/usr/bin/env python3
"""
Extracteur UQload avancé avec contournement Cloudflare
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re
import random

class AdvancedUQloadExtractor:
    """Extracteur UQload avancé avec techniques anti-détection"""
    
    def __init__(self, visible=True):
        self.visible = visible
        self.driver = None
        
    def setup_stealth_driver(self):
        """Configure un driver Selenium furtif"""
        chrome_options = Options()
        
        if not self.visible:
            chrome_options.add_argument("--headless")
        
        # Options anti-détection avancées
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")  # Plus rapide
        chrome_options.add_argument("--disable-javascript")  # Puis on l'active manuellement
        
        # User agent aléatoire
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        # Désactiver l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Prefs pour éviter la détection
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts anti-détection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr']})")
            
            # Taille de fenêtre aléatoire
            widths = [1366, 1920, 1440, 1280]
            heights = [768, 1080, 900, 720]
            self.driver.set_window_size(random.choice(widths), random.choice(heights))
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur setup driver: {e}")
            return False
    
    def wait_for_cloudflare(self, max_wait=30):
        """Attend que Cloudflare laisse passer"""
        print("⏳ Attente de Cloudflare...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                # Vérifier si on est toujours sur la page Cloudflare
                title = self.driver.title.lower()
                if "instant" in title or "moment" in title or "cloudflare" in title:
                    print("   🔄 Cloudflare actif, attente...")
                    time.sleep(2)
                    continue
                else:
                    print("   ✅ Cloudflare passé!")
                    return True
                    
            except Exception:
                time.sleep(1)
                continue
        
        print("   ⚠️ Timeout Cloudflare")
        return False
    
    def find_uqload_button(self):
        """Trouve le bouton UQload avec plusieurs méthodes"""
        print("🔍 Recherche du bouton UQload...")
        
        # Méthode 1: Sélecteurs CSS spécifiques
        uqload_selectors = [
            'a[href*="uqload"]',
            'button[onclick*="uqload"]',
            '[data-server*="uqload"]',
            '[data-video*="uqload"]',
            '.server-item[data-name*="uqload"]',
            'a:contains("UQload")',
            'button:contains("UQload")',
            '[title*="uqload"]'
        ]
        
        for selector in uqload_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"   ✅ Trouvé avec sélecteur: {selector}")
                    return elements[0]
            except:
                continue
        
        # Méthode 2: Recherche dans tous les éléments cliquables
        print("   🔍 Recherche élargie...")
        clickable_elements = self.driver.find_elements(By.CSS_SELECTOR, 
            "a, button, [onclick], [data-server], .server-item, .player-server, .btn")
        
        for element in clickable_elements:
            try:
                text = element.text.lower()
                href = element.get_attribute('href') or ''
                onclick = element.get_attribute('onclick') or ''
                data_server = element.get_attribute('data-server') or ''
                title = element.get_attribute('title') or ''
                
                # Chercher "uqload" dans tous les attributs
                if any('uqload' in attr.lower() for attr in [text, href, onclick, data_server, title]):
                    print(f"   ✅ UQload trouvé: '{text}' - {element.tag_name}")
                    return element
                    
            except Exception:
                continue
        
        # Méthode 3: Recherche par XPath
        print("   🔍 Recherche XPath...")
        xpath_selectors = [
            "//a[contains(text(), 'UQload')]",
            "//button[contains(text(), 'UQload')]",
            "//a[contains(@href, 'uqload')]",
            "//*[contains(@data-server, 'uqload')]"
        ]
        
        for xpath in xpath_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, xpath)
                if elements:
                    print(f"   ✅ Trouvé avec XPath")
                    return elements[0]
            except:
                continue
        
        print("   ❌ Bouton UQload non trouvé")
        return None
    
    def click_element_safely(self, element):
        """Clique sur un élément de manière sûre"""
        try:
            # Scroll vers l'élément
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(1)
            
            # Attendre que l'élément soit cliquable
            WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(element))
            
            # Essayer le clic normal
            try:
                element.click()
                return True
            except:
                # Essayer avec JavaScript
                self.driver.execute_script("arguments[0].click();", element)
                return True
                
        except Exception as e:
            print(f"   ⚠️ Erreur clic: {e}")
            return False
    
    def find_and_click_play(self):
        """Trouve et clique sur le bouton play"""
        print("🔍 Recherche du bouton play...")
        
        # Attendre un peu que le lecteur se charge
        time.sleep(3)
        
        play_selectors = [
            '.play-button',
            '.video-play-button',
            '[class*="play"]',
            'button[class*="play"]',
            '.btn-play',
            '#play-button',
            '.player-play',
            '[onclick*="play"]',
            'button[aria-label*="play"]'
        ]
        
        for selector in play_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ Bouton play trouvé: {selector}")
                        if self.click_element_safely(element):
                            return True
            except:
                continue
        
        # Chercher dans les iframes
        print("   🔍 Recherche dans les iframes...")
        iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
        
        for iframe in iframes:
            try:
                self.driver.switch_to.frame(iframe)
                
                for selector in play_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed():
                                print(f"   ✅ Play trouvé dans iframe: {selector}")
                                if self.click_element_safely(element):
                                    self.driver.switch_to.default_content()
                                    return True
                    except:
                        continue
                
                self.driver.switch_to.default_content()
                
            except:
                self.driver.switch_to.default_content()
                continue
        
        print("   ❌ Bouton play non trouvé")
        return False
    
    def extract_video_urls(self):
        """Extrait les URLs vidéo"""
        print("🎥 Extraction des liens vidéo...")
        
        video_urls = []
        
        # Attendre que la vidéo se charge
        time.sleep(5)
        
        try:
            # Méthode 1: Éléments video et source
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"   📹 Video src: {src}")
                
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append(src)
                        print(f"   📹 Source src: {src}")
            
            # Méthode 2: Analyse du code source
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?',
                r'"(https?://[^"]+uqload[^"]+\.(?:mp4|m3u8))"'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"   📹 Regex match: {match}")
            
            # Méthode 3: Requêtes réseau
            try:
                network_urls = self.driver.execute_script("""
                    var urls = [];
                    if (window.performance && window.performance.getEntriesByType) {
                        var entries = window.performance.getEntriesByType('resource');
                        for (var i = 0; i < entries.length; i++) {
                            var url = entries[i].name;
                            if (url.includes('.mp4') || url.includes('.m3u8') || 
                                url.includes('video') || url.includes('stream')) {
                                urls.push(url);
                            }
                        }
                    }
                    return urls;
                """)
                
                for url in network_urls:
                    if url not in video_urls:
                        video_urls.append(url)
                        print(f"   📹 Network: {url}")
                        
            except Exception as e:
                print(f"   ⚠️ Erreur network: {e}")
            
        except Exception as e:
            print(f"   ⚠️ Erreur extraction: {e}")
        
        return list(set(video_urls))
    
    def extract_from_url(self, url):
        """Extrait le lien vidéo depuis l'URL"""
        print("🎬 Extracteur UQload Avancé")
        print("=" * 50)
        print(f"🔗 URL: {url}")
        
        try:
            if not self.setup_stealth_driver():
                return None
            
            # Charger la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            
            # Attendre Cloudflare
            if not self.wait_for_cloudflare():
                print("⚠️ Cloudflare n'a pas laissé passer, continuons quand même...")
            
            # Attendre le chargement complet
            time.sleep(5)
            
            # Chercher UQload
            uqload_button = self.find_uqload_button()
            
            if not uqload_button:
                print("❌ Bouton UQload non trouvé")
                return self.list_available_servers()
            
            # Cliquer sur UQload
            print("🖱️ Clic sur UQload...")
            if not self.click_element_safely(uqload_button):
                print("❌ Impossible de cliquer sur UQload")
                return None
            
            print("✅ UQload cliqué!")
            time.sleep(3)
            
            # Chercher et cliquer sur play
            if self.find_and_click_play():
                print("✅ Bouton play cliqué!")
                time.sleep(5)
            else:
                print("⚠️ Bouton play non trouvé, extraction directe...")
            
            # Extraire les liens vidéo
            video_urls = self.extract_video_urls()
            
            if video_urls:
                result = {
                    'url': url,
                    'video_links': video_urls,
                    'primary_link': video_urls[0],
                    'extraction_method': 'advanced_automation',
                    'success': True,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # Sauvegarder
                with open('advanced_uqload_extraction.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("💾 Résultats sauvegardés!")
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return None
        
        finally:
            if self.driver:
                input("Appuyez sur Entrée pour fermer le navigateur...")
                self.driver.quit()
    
    def list_available_servers(self):
        """Liste les serveurs disponibles"""
        print("📺 Serveurs disponibles:")
        
        try:
            servers = self.driver.find_elements(By.CSS_SELECTOR, 
                "a, button, [data-server], .server-item, .player-server")
            
            found_servers = []
            for server in servers:
                text = server.text.strip()
                if text and len(text) < 30 and text not in found_servers:
                    found_servers.append(text)
            
            for server in found_servers[:10]:  # Limiter à 10
                print(f"   • {server}")
                
        except Exception as e:
            print(f"   ⚠️ Erreur: {e}")
        
        return None

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎬 Extracteur UQload Avancé")
    print("=" * 60)
    print("🎯 Extraction automatique avec contournement Cloudflare")
    print()
    
    extractor = AdvancedUQloadExtractor(visible=True)
    result = extractor.extract_from_url(url)
    
    if result and result['success']:
        print("\n" + "="*60)
        print("🎉 EXTRACTION RÉUSSIE!")
        print("="*60)
        
        print(f"🎯 LIEN VIDÉO PRINCIPAL:")
        print(f"   {result['primary_link']}")
        
        if len(result['video_links']) > 1:
            print(f"\n📹 TOUS LES LIENS ({len(result['video_links'])}):")
            for i, link in enumerate(result['video_links'], 1):
                print(f"   {i}. {link}")
        
        print(f"\n💾 Résultats dans: advanced_uqload_extraction.json")
        
    else:
        print("\n❌ Extraction automatique échouée")
        print("💡 Utilisez le guide manuel: python simple_uqload_guide.py")

if __name__ == "__main__":
    main()
