# French Stream Movie Extractor

A comprehensive Python tool to extract download links from French streaming sites, specifically designed to handle JavaScript-rendered content and provide batch processing capabilities.

## Features

- 🎬 **Single Movie Extraction**: Extract download and streaming links from individual movie pages
- 📦 **Batch Processing**: Process multiple movie URLs from a file
- 🔍 **Auto Discovery**: Automatically discover movie URLs from site catalogs
- 🌐 **JavaScript Support**: Handles JavaScript-rendered content using Selenium
- 📄 **Multiple Formats**: Supports both Selenium (for JS sites) and BeautifulSoup (for static sites)
- 💾 **Export Results**: Save results to JSON format
- 🎯 **Smart Extraction**: Automatically detects download links, streaming links, quality, and metadata

## Installation

1. **Install Python Dependencies**:
```bash
pip install -r requirements.txt
```

2. **Install Chrome/Chromium** (required for Selenium):
   - Download and install Google Chrome or Chromium browser
   - The tool will automatically download the appropriate ChromeDriver

## Usage

### Command Line Interface

#### Extract Single Movie
```bash
python french_stream_extractor.py "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
```

#### Batch Processing from File
```bash
# Create a file with URLs (one per line)
echo "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html" > urls.txt
python french_stream_extractor.py urls.txt --batch
```

#### Auto-Discovery from Catalog
```bash
python french_stream_extractor.py "https://vvw.french-stream.bio" --discover --max-pages 3
```

#### Advanced Options
```bash
python french_stream_extractor.py URL [OPTIONS]

Options:
  --batch              Process URLs from file (one per line)
  --discover           Discover movie URLs from site catalog
  --max-pages N        Maximum pages to crawl for discovery (default: 5)
  --output FILE        Output JSON file (default: extracted_movies.json)
  --headless           Run browser in headless mode (default: True)
  --timeout N          Request timeout in seconds (default: 30)
  --verbose, -v        Enable verbose logging
```

### Python API

```python
from french_stream_extractor import FrenchStreamExtractor

# Initialize extractor
extractor = FrenchStreamExtractor(headless=True, timeout=30)

# Extract single movie
movie_info = extractor.extract_movie_info("https://example.com/movie")

if movie_info:
    print(f"Title: {movie_info.title}")
    print(f"Download links: {movie_info.download_links}")
    print(f"Streaming links: {movie_info.streaming_links}")

# Batch processing
urls = ["url1", "url2", "url3"]
results = extractor.extract_multiple_movies(urls)

# Save results
extractor.save_results_to_json(results, "movies.json")

# Auto-discovery
discovered_urls = extractor.discover_movie_urls("https://site.com", max_pages=5)
```

## Example Output

```json
[
  {
    "title": "Dis-moi juste que tu m'aimes",
    "url": "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html",
    "download_links": [
      "https://mega.nz/file/example",
      "https://1fichier.com/example"
    ],
    "streaming_links": [
      "https://player.example.com/embed/123"
    ],
    "quality": "1080p",
    "year": "2023"
  }
]
```

## Testing

Run the example test suite:

```bash
python example_usage.py
```

This will test:
- Single movie extraction with the provided URL
- Batch processing capabilities
- Movie discovery from catalog pages

## Supported Sites

The tool is designed to work with French streaming sites that follow common patterns:
- Sites using JavaScript for content loading
- Sites with standard HTML structure
- Common download link patterns (Mega, 1fichier, Uptobox, MediaFire, etc.)

## Technical Details

### Extraction Methods

1. **Selenium WebDriver**: For JavaScript-heavy sites
   - Handles dynamic content loading
   - Waits for elements to appear
   - Executes JavaScript

2. **BeautifulSoup + Requests**: For static HTML sites
   - Faster processing
   - Lower resource usage
   - Fallback method

### Link Detection

The tool uses multiple strategies to find download links:
- CSS selectors for common download button patterns
- Regular expressions for direct file links
- iframe source extraction for embedded players
- Text pattern matching for various hosting services

## Troubleshooting

### Common Issues

1. **"pip not found"**: Install pip or use `python -m pip install -r requirements.txt`
2. **Chrome/ChromeDriver issues**: Ensure Chrome is installed and accessible
3. **JavaScript timeout**: Increase timeout value with `--timeout 60`
4. **No links found**: Site may have changed structure or uses different patterns

### Dependencies Issues

If you encounter import errors, install dependencies manually:

```bash
pip install selenium beautifulsoup4 requests lxml webdriver-manager click colorama tqdm fake-useragent
```

## Legal Notice

This tool is for educational purposes only. Always respect website terms of service and copyright laws. Use responsibly and ensure you have permission to access and download content.

## Contributing

Feel free to contribute by:
- Adding support for new sites
- Improving extraction patterns
- Fixing bugs
- Adding new features

## License

This project is provided as-is for educational purposes.
