#!/usr/bin/env python3
"""
Simplified French Stream Movie Extractor
Works with basic Python libraries for demonstration
"""

import re
import json
import urllib.request
import urllib.parse
from html.parser import HTMLParser
from typing import List, Dict, Optional

class MovieLinkParser(HTMLParser):
    """HTML parser to extract movie links and information"""
    
    def __init__(self):
        super().__init__()
        self.title = ""
        self.download_links = []
        self.streaming_links = []
        self.current_tag = ""
        self.in_title = False
        
        # Patterns for download services
        self.download_patterns = [
            r'https?://mega\.nz/[^\s"\'<>]+',
            r'https?://1fichier\.com/[^\s"\'<>]+',
            r'https?://uptobox\.com/[^\s"\'<>]+',
            r'https?://mediafire\.com/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+\.(?:mp4|mkv|avi|mov|wmv|flv|webm)',
            r'https?://[^\s"\'<>]*(?:download|dl|stream)[^\s"\'<>]*'
        ]
    
    def handle_starttag(self, tag, attrs):
        self.current_tag = tag
        
        # Check for title tags
        if tag in ['h1', 'title']:
            self.in_title = True
        
        # Check for links
        if tag == 'a':
            href = dict(attrs).get('href', '')
            if href:
                # Check if it's a download link
                for pattern in self.download_patterns:
                    if re.search(pattern, href, re.IGNORECASE):
                        if href not in self.download_links:
                            self.download_links.append(href)
                        break
        
        # Check for iframes (streaming)
        if tag == 'iframe':
            src = dict(attrs).get('src', '')
            if src and any(keyword in src.lower() for keyword in ['player', 'stream', 'embed']):
                if src not in self.streaming_links:
                    self.streaming_links.append(src)
    
    def handle_data(self, data):
        if self.in_title and self.current_tag in ['h1', 'title']:
            if data.strip() and not self.title:
                self.title = data.strip()
    
    def handle_endtag(self, tag):
        if tag in ['h1', 'title']:
            self.in_title = False

class SimpleFrenchStreamExtractor:
    """Simplified extractor using only standard Python libraries"""
    
    def __init__(self, timeout: int = 30):
        self.timeout = timeout
        
        # User agent to avoid basic blocking
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
    
    def extract_movie_info(self, url: str) -> Optional[Dict]:
        """Extract movie information from URL"""
        print(f"🔍 Extracting from: {url}")
        
        try:
            # Create request with headers
            req = urllib.request.Request(url, headers=self.headers)
            
            # Open URL
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                html_content = response.read().decode('utf-8', errors='ignore')
            
            # Parse HTML
            parser = MovieLinkParser()
            parser.feed(html_content)
            
            # Also search for links in the raw HTML using regex
            additional_links = self._extract_links_regex(html_content)
            
            # Combine results
            all_download_links = list(set(parser.download_links + additional_links))
            
            # Extract additional metadata
            quality = self._extract_quality(html_content)
            year = self._extract_year(html_content)
            
            movie_info = {
                'title': parser.title or self._extract_title_from_url(url),
                'url': url,
                'download_links': all_download_links,
                'streaming_links': parser.streaming_links,
                'quality': quality,
                'year': year
            }
            
            return movie_info
            
        except Exception as e:
            print(f"❌ Error extracting from {url}: {e}")
            return None
    
    def _extract_links_regex(self, html_content: str) -> List[str]:
        """Extract download links using regex patterns"""
        links = []
        
        patterns = [
            r'https?://mega\.nz/[^\s"\'<>]+',
            r'https?://1fichier\.com/[^\s"\'<>]+',
            r'https?://uptobox\.com/[^\s"\'<>]+',
            r'https?://mediafire\.com/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+\.(?:mp4|mkv|avi|mov|wmv|flv|webm)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            links.extend(matches)
        
        return list(set(links))
    
    def _extract_quality(self, html_content: str) -> Optional[str]:
        """Extract video quality from HTML content"""
        quality_patterns = [
            r'(\d{3,4}p)',
            r'(HD|SD|4K|1080p|720p|480p)',
            r'(BluRay|DVDRip|WEBRip|HDTV)'
        ]
        
        for pattern in quality_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_year(self, html_content: str) -> Optional[str]:
        """Extract movie year from HTML content"""
        year_pattern = r'(19|20)\d{2}'
        matches = re.findall(year_pattern, html_content)
        
        if matches:
            # Return the most recent year found
            years = [int(year) for year in matches if len(year) == 4]
            if years:
                return str(max(years))
        
        return None
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract title from URL as fallback"""
        try:
            # Extract filename from URL
            path = urllib.parse.urlparse(url).path
            filename = path.split('/')[-1]
            
            # Clean up the filename
            title = filename.replace('-', ' ').replace('_', ' ')
            title = re.sub(r'\.(html|php)$', '', title, re.IGNORECASE)
            
            return title.title()
        except:
            return "Unknown Title"
    
    def save_results_to_json(self, results: List[Dict], filename: str = "extracted_movies.json"):
        """Save results to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"💾 Results saved to {filename}")
        except Exception as e:
            print(f"❌ Failed to save results: {e}")
    
    def print_results(self, movie_info: Dict):
        """Print extraction results"""
        if not movie_info:
            print("❌ No results to display.")
            return
        
        print(f"\n{'='*60}")
        print(f"🎬 MOVIE: {movie_info['title']}")
        print(f"{'='*60}")
        print(f"🔗 URL: {movie_info['url']}")
        
        if movie_info.get('year'):
            print(f"📅 Year: {movie_info['year']}")
        if movie_info.get('quality'):
            print(f"🎬 Quality: {movie_info['quality']}")
        
        download_links = movie_info.get('download_links', [])
        if download_links:
            print(f"\n📥 DOWNLOAD LINKS ({len(download_links)} found):")
            for i, link in enumerate(download_links, 1):
                print(f"  {i}. {link}")
        else:
            print("\n📥 No download links found")
        
        streaming_links = movie_info.get('streaming_links', [])
        if streaming_links:
            print(f"\n📺 STREAMING LINKS ({len(streaming_links)} found):")
            for i, link in enumerate(streaming_links, 1):
                print(f"  {i}. {link}")
        else:
            print("\n📺 No streaming links found")

def main():
    """Main function to test the extractor"""
    print("🎬 Simple French Stream Movie Extractor")
    print("=" * 50)
    
    # The URL provided by the user
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    # Initialize extractor
    extractor = SimpleFrenchStreamExtractor(timeout=30)
    
    # Extract movie information
    movie_info = extractor.extract_movie_info(url)
    
    if movie_info:
        # Display results
        extractor.print_results(movie_info)
        
        # Save to JSON
        extractor.save_results_to_json([movie_info], "simple_extraction_results.json")
        
        # Return the main download link if found
        download_links = movie_info.get('download_links', [])
        if download_links:
            print(f"\n🎯 PRIMARY DOWNLOAD LINK:")
            print(f"   {download_links[0]}")
            return download_links[0]
        else:
            print("\n⚠️  No download links were found. The site may require JavaScript or have changed its structure.")
            return None
    else:
        print("❌ Failed to extract movie information")
        return None

if __name__ == "__main__":
    main()
