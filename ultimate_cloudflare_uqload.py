#!/usr/bin/env python3
"""
EXTRACTEUR ULTIMATE: Cloudflare + UQload
1. Contourne Cloudflare automatiquement (case à cocher)
2. Trouve et clique sur UQload
3. Clique sur play
4. Extrait le lien vidéo
TOUT AUTOMATIQUEMENT !
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import random
import json
import re

class UltimateCloudflareUQloadExtractor:
    """Extracteur ultimate: Cloudflare + UQload en une seule fois"""
    
    def __init__(self, show_browser=True):
        self.show_browser = show_browser
        self.driver = None
        
    def setup_ultimate_driver(self):
        """Configure le driver ultimate"""
        print("🚀 Configuration du navigateur ULTIMATE...")
        
        chrome_options = Options()
        
        if not self.show_browser:
            chrome_options.add_argument("--headless")
        
        # Options ultimate pour contourner toutes les détections
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        # User agent ultra-réaliste
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Désactiver l'automation
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Prefs ultimate
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.media_stream": 1,
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts anti-détection ultimate
            ultimate_script = """
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr']});
            Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
            Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
            Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
            Object.defineProperty(screen, 'width', {get: () => 1920});
            Object.defineProperty(screen, 'height', {get: () => 1080});
            window.chrome = {runtime: {}};
            delete navigator.__proto__.webdriver;
            """
            self.driver.execute_script(ultimate_script)
            
            # Taille réaliste
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur ULTIMATE configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def bypass_cloudflare_ultimate(self):
        """Contourne Cloudflare avec méthodes ultimate"""
        print("🛡️ Contournement Cloudflare ULTIMATE...")
        
        max_attempts = 5
        for attempt in range(max_attempts):
            print(f"   🔄 Tentative {attempt + 1}/{max_attempts}")
            
            # Attendre le chargement
            time.sleep(random.uniform(3, 6))
            
            # Vérifier si Cloudflare est actif
            try:
                current_title = self.driver.title.lower()
                if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                    # Vérifier le contenu
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    if body and len(body.text) > 1000:
                        print("   ✅ Cloudflare déjà passé!")
                        return True
            except:
                pass
            
            # Chercher la case à cocher
            checkbox_found = False
            
            # Sélecteurs ultimate pour Cloudflare
            checkbox_selectors = [
                'input[type="checkbox"]',
                '.cf-turnstile input',
                '#cf-turnstile input',
                '[data-sitekey] input',
                '.recaptcha-checkbox',
                '#recaptcha-anchor',
                '.challenge-form input[type="checkbox"]',
                'label input[type="checkbox"]'
            ]
            
            # Recherche directe
            for selector in checkbox_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"   ✅ Case trouvée: {selector}")
                            if self.ultimate_click(element):
                                checkbox_found = True
                                break
                    if checkbox_found:
                        break
                except:
                    continue
            
            # Recherche dans les iframes
            if not checkbox_found:
                print("   🔍 Recherche dans iframes...")
                iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                
                for iframe in iframes:
                    try:
                        iframe_src = iframe.get_attribute('src') or ''
                        if any(keyword in iframe_src.lower() for keyword in ['cloudflare', 'captcha', 'turnstile', 'challenge']):
                            self.driver.switch_to.frame(iframe)
                            
                            for selector in checkbox_selectors:
                                try:
                                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                    for element in elements:
                                        if element.is_displayed():
                                            print(f"   ✅ Case iframe trouvée: {selector}")
                                            if self.ultimate_click(element):
                                                checkbox_found = True
                                                break
                                    if checkbox_found:
                                        break
                                except:
                                    continue
                            
                            self.driver.switch_to.default_content()
                            if checkbox_found:
                                break
                    except:
                        self.driver.switch_to.default_content()
                        continue
            
            if checkbox_found:
                # Attendre la vérification
                print("   ⏳ Attente vérification...")
                time.sleep(random.uniform(5, 8))
                
                # Vérifier si on a passé
                try:
                    current_title = self.driver.title.lower()
                    if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare"]):
                        body = self.driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 1000:
                            print("   ✅ Cloudflare contourné!")
                            return True
                except:
                    pass
            
            # Attendre avant la prochaine tentative
            if attempt < max_attempts - 1:
                time.sleep(random.uniform(3, 5))
        
        print("   ❌ Cloudflare non contourné")
        return False
    
    def ultimate_click(self, element):
        """Clic ultimate qui imite parfaitement un humain"""
        try:
            # Scroll naturel
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", element)
            time.sleep(random.uniform(1, 2))
            
            # Mouvement de souris naturel
            actions = ActionChains(self.driver)
            actions.move_to_element(element)
            time.sleep(random.uniform(0.3, 0.8))
            
            # Clic avec délai humain
            actions.click(element)
            actions.perform()
            
            time.sleep(random.uniform(1, 2))
            return True
            
        except:
            # Fallback JavaScript
            try:
                self.driver.execute_script("arguments[0].click();", element)
                time.sleep(random.uniform(1, 2))
                return True
            except:
                return False
    
    def find_uqload_ultimate(self):
        """Trouve UQload avec méthodes ultimate"""
        print("🔍 Recherche UQload ULTIMATE...")
        
        # Attendre le chargement complet
        time.sleep(5)
        
        # Sélecteurs ultimate pour UQload
        uqload_selectors = [
            'a[href*="uqload" i]',
            'button[onclick*="uqload" i]',
            '[data-server*="uqload" i]',
            '[data-video*="uqload" i]',
            '[data-name*="uqload" i]',
            '.server-item[data-server*="uqload" i]',
            'a[title*="uqload" i]',
            'button[title*="uqload" i]'
        ]
        
        # Recherche directe
        for selector in uqload_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ UQload trouvé: {selector}")
                        return element
            except:
                continue
        
        # Recherche textuelle ultimate
        print("   🔍 Recherche textuelle...")
        xpath_selectors = [
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]",
            "//span[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'uqload')]//parent::*"
        ]
        
        for xpath in xpath_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ UQload XPath trouvé")
                        return element
            except:
                continue
        
        # Analyse exhaustive
        print("   🔍 Analyse exhaustive...")
        clickable_elements = self.driver.find_elements(By.CSS_SELECTOR, 
            "a, button, [onclick], [data-server], .server-item, .player-server, .btn")
        
        for element in clickable_elements:
            try:
                if not (element.is_displayed() and element.is_enabled()):
                    continue
                
                text = element.text.lower()
                href = (element.get_attribute('href') or '').lower()
                onclick = (element.get_attribute('onclick') or '').lower()
                data_server = (element.get_attribute('data-server') or '').lower()
                title = (element.get_attribute('title') or '').lower()
                
                if any('uqload' in attr for attr in [text, href, onclick, data_server, title]):
                    print(f"   ✅ UQload exhaustif trouvé: '{text}'")
                    return element
                    
            except:
                continue
        
        print("   ❌ UQload non trouvé")
        return None
    
    def find_play_ultimate(self):
        """Trouve le bouton play avec méthodes ultimate"""
        print("▶️ Recherche play ULTIMATE...")
        
        time.sleep(5)
        
        play_selectors = [
            '.play-button',
            '.video-play-button',
            '.btn-play',
            '#play-button',
            '[class*="play"]',
            'button[class*="play"]',
            '[onclick*="play"]',
            '.vjs-big-play-button',
            '.plyr__control--overlaid',
            '[aria-label*="play" i]'
        ]
        
        # Recherche directe
        for selector in play_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"   ✅ Play trouvé: {selector}")
                        return element
            except:
                continue
        
        # Recherche dans iframes
        print("   🔍 Recherche play dans iframes...")
        iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
        
        for iframe in iframes:
            try:
                self.driver.switch_to.frame(iframe)
                
                for selector in play_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed():
                                print(f"   ✅ Play iframe trouvé: {selector}")
                                return element, iframe
                    except:
                        continue
                
                self.driver.switch_to.default_content()
                
            except:
                self.driver.switch_to.default_content()
                continue
        
        print("   ❌ Play non trouvé")
        return None
    
    def extract_video_ultimate(self):
        """Extrait les liens vidéo avec méthodes ultimate"""
        print("🎥 Extraction vidéo ULTIMATE...")
        
        time.sleep(8)
        video_urls = []
        
        try:
            # Méthode 1: Éléments video
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"   📹 Video: {src}")
            
            # Méthode 2: Regex ultimate
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+uqload[^"\s]*\.(?:mp4|m3u8|webm)',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"   📹 Regex: {match}")
            
            # Méthode 3: Network
            try:
                network_urls = self.driver.execute_script("""
                    var urls = [];
                    if (window.performance && window.performance.getEntriesByType) {
                        var entries = window.performance.getEntriesByType('resource');
                        for (var i = 0; i < entries.length; i++) {
                            var url = entries[i].name;
                            if (url.includes('.mp4') || url.includes('.m3u8') || 
                                url.includes('video') || url.includes('uqload')) {
                                urls.push(url);
                            }
                        }
                    }
                    return urls;
                """)
                
                for url in network_urls:
                    if url not in video_urls and url.startswith('http'):
                        video_urls.append(url)
                        print(f"   📹 Network: {url}")
                        
            except Exception as e:
                print(f"   ⚠️ Erreur network: {e}")
            
        except Exception as e:
            print(f"   ⚠️ Erreur extraction: {e}")
        
        return list(set(video_urls))
    
    def run_ultimate_extraction(self, url):
        """Lance l'extraction ultimate complète"""
        print("🚀 EXTRACTEUR ULTIMATE - CLOUDFLARE + UQLOAD")
        print("=" * 70)
        print("🎯 Objectif: Contourner Cloudflare ET extraire UQload")
        print(f"🔗 URL: {url}")
        print()
        
        try:
            # Étape 1: Setup
            if not self.setup_ultimate_driver():
                return None
            
            # Étape 2: Charger la page
            print("📡 Chargement de la page...")
            self.driver.get(url)
            
            # Étape 3: Contourner Cloudflare
            if not self.bypass_cloudflare_ultimate():
                print("❌ Impossible de contourner Cloudflare")
                return None
            
            print("✅ Cloudflare contourné! Accès au site obtenu!")
            
            # Étape 4: Chercher UQload
            uqload_button = self.find_uqload_ultimate()
            if not uqload_button:
                print("❌ UQload non trouvé")
                return None
            
            # Étape 5: Cliquer sur UQload
            print("🖱️ Clic sur UQload...")
            if not self.ultimate_click(uqload_button):
                print("❌ Impossible de cliquer sur UQload")
                return None
            
            print("✅ UQload cliqué!")
            time.sleep(5)
            
            # Étape 6: Chercher et cliquer play
            play_result = self.find_play_ultimate()
            if play_result:
                if isinstance(play_result, tuple):
                    element, iframe = play_result
                    self.driver.switch_to.frame(iframe)
                    self.ultimate_click(element)
                    self.driver.switch_to.default_content()
                else:
                    self.ultimate_click(play_result)
                
                print("✅ Play cliqué!")
                time.sleep(8)
            else:
                print("⚠️ Play non trouvé - extraction directe...")
            
            # Étape 7: Extraire les liens vidéo
            video_urls = self.extract_video_ultimate()
            
            if video_urls:
                result = {
                    'url': url,
                    'video_links': video_urls,
                    'primary_link': video_urls[0],
                    'extraction_method': 'ultimate_cloudflare_uqload',
                    'success': True,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'cloudflare_bypassed': True,
                    'uqload_found': True
                }
                
                # Sauvegarder
                with open('ultimate_extraction_result.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("\n" + "="*70)
                print("🎉 EXTRACTION ULTIMATE RÉUSSIE!")
                print("="*70)
                
                print(f"🎯 LIEN VIDÉO PRINCIPAL:")
                print(f"   {result['primary_link']}")
                
                if len(video_urls) > 1:
                    print(f"\n📹 TOUS LES LIENS ({len(video_urls)}):")
                    for i, link in enumerate(video_urls, 1):
                        print(f"   {i}. {link}")
                
                print(f"\n💾 Résultats: ultimate_extraction_result.json")
                
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur fatale: {e}")
            return None
        
        finally:
            if self.driver:
                print("\n⏳ Fermeture dans 15 secondes...")
                time.sleep(15)
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🚀 EXTRACTEUR ULTIMATE")
    print("=" * 70)
    print("✨ CONTOURNE CLOUDFLARE + EXTRAIT UQLOAD")
    print("🤖 100% AUTOMATIQUE - AUCUNE INTERVENTION")
    print()
    
    extractor = UltimateCloudflareUQloadExtractor(show_browser=True)
    result = extractor.run_ultimate_extraction(url)
    
    if result and result['success']:
        print(f"\n🏆 MISSION ULTIMATE ACCOMPLIE!")
        print(f"🎬 Lien vidéo UQload extrait:")
        print(f"🔗 {result['primary_link']}")
    else:
        print(f"\n❌ Extraction ultimate échouée")

if __name__ == "__main__":
    main()
