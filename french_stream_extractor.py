#!/usr/bin/env python3
"""
French Stream Movie Extractor
A tool to extract download links from French streaming sites
"""

import re
import time
import json
import logging
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import requests
    from bs4 import BeautifulSoup
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from fake_useragent import UserAgent
    FAKE_UA_AVAILABLE = True
except ImportError:
    FAKE_UA_AVAILABLE = False


@dataclass
class MovieInfo:
    """Data class to store movie information"""
    title: str
    url: str
    download_links: List[str]
    streaming_links: List[str]
    quality: Optional[str] = None
    language: Optional[str] = None
    size: Optional[str] = None
    year: Optional[str] = None


class FrenchStreamExtractor:
    """Main class for extracting movie links from French streaming sites"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        """
        Initialize the extractor
        
        Args:
            headless: Run browser in headless mode
            timeout: Timeout for web requests
        """
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.session = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Common patterns for download links
        self.download_patterns = [
            r'https?://[^"\s]+\.(?:mp4|mkv|avi|mov|wmv|flv|webm)',
            r'https?://[^"\s]*(?:download|dl|stream)[^"\s]*',
            r'https?://(?:mega\.nz|mediafire\.com|1fichier\.com|uptobox\.com)[^"\s]*',
            r'https?://[^"\s]*\.(?:torrent)',
        ]
        
        # Setup user agent
        if FAKE_UA_AVAILABLE:
            self.ua = UserAgent()
        else:
            self.ua = None
    
    def _get_user_agent(self) -> str:
        """Get a random user agent"""
        if self.ua:
            return self.ua.random
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    def _setup_selenium(self) -> bool:
        """Setup Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            self.logger.error("Selenium not available. Install with: pip install selenium webdriver-manager")
            return False
        
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument(f"--user-agent={self._get_user_agent()}")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(self.timeout)
            return True
        except Exception as e:
            self.logger.error(f"Failed to setup Selenium: {e}")
            return False
    
    def _setup_requests(self) -> bool:
        """Setup requests session"""
        if not REQUESTS_AVAILABLE:
            self.logger.error("Requests not available. Install with: pip install requests beautifulsoup4")
            return False
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self._get_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        return True

    def extract_movie_info(self, url: str) -> Optional[MovieInfo]:
        """
        Extract movie information from a given URL

        Args:
            url: Movie page URL

        Returns:
            MovieInfo object or None if extraction fails
        """
        self.logger.info(f"Extracting movie info from: {url}")

        # Try Selenium first for JavaScript-heavy sites
        movie_info = self._extract_with_selenium(url)
        if movie_info:
            return movie_info

        # Fallback to requests
        movie_info = self._extract_with_requests(url)
        return movie_info

    def _extract_with_selenium(self, url: str) -> Optional[MovieInfo]:
        """Extract movie info using Selenium"""
        if not self._setup_selenium():
            return None

        try:
            self.driver.get(url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Extract title
            title = self._extract_title_selenium()

            # Extract download links
            download_links = self._extract_download_links_selenium()

            # Extract streaming links
            streaming_links = self._extract_streaming_links_selenium()

            # Extract additional info
            quality = self._extract_quality_selenium()
            year = self._extract_year_selenium()

            return MovieInfo(
                title=title or "Unknown Title",
                url=url,
                download_links=download_links,
                streaming_links=streaming_links,
                quality=quality,
                year=year
            )

        except Exception as e:
            self.logger.error(f"Selenium extraction failed: {e}")
            return None
        finally:
            if self.driver:
                self.driver.quit()

    def _extract_with_requests(self, url: str) -> Optional[MovieInfo]:
        """Extract movie info using requests"""
        if not self._setup_requests():
            return None

        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract title
            title = self._extract_title_bs4(soup)

            # Extract download links
            download_links = self._extract_download_links_bs4(soup, url)

            # Extract streaming links
            streaming_links = self._extract_streaming_links_bs4(soup, url)

            # Extract additional info
            quality = self._extract_quality_bs4(soup)
            year = self._extract_year_bs4(soup)

            return MovieInfo(
                title=title or "Unknown Title",
                url=url,
                download_links=download_links,
                streaming_links=streaming_links,
                quality=quality,
                year=year
            )

        except Exception as e:
            self.logger.error(f"Requests extraction failed: {e}")
            return None

    def _extract_title_selenium(self) -> Optional[str]:
        """Extract movie title using Selenium"""
        try:
            # Common title selectors for French streaming sites
            selectors = [
                'h1.entry-title',
                'h1.post-title',
                '.movie-title h1',
                '.film-title',
                'h1',
                '.title'
            ]

            for selector in selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.text.strip():
                        return element.text.strip()
                except:
                    continue

            # Fallback to page title
            return self.driver.title
        except Exception as e:
            self.logger.error(f"Failed to extract title with Selenium: {e}")
            return None

    def _extract_download_links_selenium(self) -> List[str]:
        """Extract download links using Selenium"""
        links = []
        try:
            # Wait for potential dynamic content
            time.sleep(3)

            # Look for download buttons and links
            download_selectors = [
                'a[href*="download"]',
                'a[href*="dl"]',
                'a[href*="mega.nz"]',
                'a[href*="mediafire"]',
                'a[href*="1fichier"]',
                'a[href*="uptobox"]',
                '.download-link a',
                '.btn-download',
                'a[class*="download"]'
            ]

            for selector in download_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and href not in links:
                            links.append(href)
                except:
                    continue

            # Also check page source for embedded links
            page_source = self.driver.page_source
            for pattern in self.download_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if match not in links:
                        links.append(match)

        except Exception as e:
            self.logger.error(f"Failed to extract download links with Selenium: {e}")

        return links

    def _extract_streaming_links_selenium(self) -> List[str]:
        """Extract streaming links using Selenium"""
        links = []
        try:
            # Look for streaming/player iframes and links
            streaming_selectors = [
                'iframe[src*="player"]',
                'iframe[src*="stream"]',
                'iframe[src*="embed"]',
                '.player iframe',
                'video source',
                'a[href*="stream"]'
            ]

            for selector in streaming_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        src = element.get_attribute('src') or element.get_attribute('href')
                        if src and src not in links:
                            links.append(src)
                except:
                    continue

        except Exception as e:
            self.logger.error(f"Failed to extract streaming links with Selenium: {e}")

        return links

    def _extract_quality_selenium(self) -> Optional[str]:
        """Extract video quality using Selenium"""
        try:
            quality_patterns = [
                r'(\d{3,4}p)',
                r'(HD|SD|4K|1080p|720p|480p)',
                r'(BluRay|DVDRip|WEBRip|HDTV)'
            ]

            page_text = self.driver.page_source
            for pattern in quality_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    return match.group(1)
        except Exception as e:
            self.logger.error(f"Failed to extract quality with Selenium: {e}")

        return None

    def _extract_year_selenium(self) -> Optional[str]:
        """Extract movie year using Selenium"""
        try:
            year_pattern = r'(19|20)\d{2}'
            page_text = self.driver.page_source
            matches = re.findall(year_pattern, page_text)
            if matches:
                # Return the most recent year found
                years = [int(match + '00') if len(match) == 2 else int(match + matches[i+1])
                        for i, match in enumerate(matches[::2])]
                return str(max(years))
        except Exception as e:
            self.logger.error(f"Failed to extract year with Selenium: {e}")

        return None

    def _extract_title_bs4(self, soup) -> Optional[str]:
        """Extract movie title using BeautifulSoup"""
        try:
            # Common title selectors
            selectors = [
                'h1.entry-title',
                'h1.post-title',
                '.movie-title h1',
                '.film-title',
                'h1',
                '.title'
            ]

            for selector in selectors:
                element = soup.select_one(selector)
                if element and element.get_text(strip=True):
                    return element.get_text(strip=True)

            # Fallback to page title
            title_tag = soup.find('title')
            if title_tag:
                return title_tag.get_text(strip=True)

        except Exception as e:
            self.logger.error(f"Failed to extract title with BS4: {e}")

        return None

    def _extract_download_links_bs4(self, soup, base_url: str) -> List[str]:
        """Extract download links using BeautifulSoup"""
        links = []
        try:
            # Look for download links
            download_selectors = [
                'a[href*="download"]',
                'a[href*="dl"]',
                'a[href*="mega.nz"]',
                'a[href*="mediafire"]',
                'a[href*="1fichier"]',
                'a[href*="uptobox"]',
                '.download-link a',
                '.btn-download',
                'a[class*="download"]'
            ]

            for selector in download_selectors:
                elements = soup.select(selector)
                for element in elements:
                    href = element.get('href')
                    if href:
                        # Convert relative URLs to absolute
                        full_url = urljoin(base_url, href)
                        if full_url not in links:
                            links.append(full_url)

            # Also search page text for embedded links
            page_text = soup.get_text()
            for pattern in self.download_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                for match in matches:
                    if match not in links:
                        links.append(match)

        except Exception as e:
            self.logger.error(f"Failed to extract download links with BS4: {e}")

        return links

    def _extract_streaming_links_bs4(self, soup, base_url: str) -> List[str]:
        """Extract streaming links using BeautifulSoup"""
        links = []
        try:
            # Look for streaming iframes and links
            streaming_selectors = [
                'iframe[src*="player"]',
                'iframe[src*="stream"]',
                'iframe[src*="embed"]',
                '.player iframe',
                'video source',
                'a[href*="stream"]'
            ]

            for selector in streaming_selectors:
                elements = soup.select(selector)
                for element in elements:
                    src = element.get('src') or element.get('href')
                    if src:
                        full_url = urljoin(base_url, src)
                        if full_url not in links:
                            links.append(full_url)

        except Exception as e:
            self.logger.error(f"Failed to extract streaming links with BS4: {e}")

        return links

    def _extract_quality_bs4(self, soup) -> Optional[str]:
        """Extract video quality using BeautifulSoup"""
        try:
            quality_patterns = [
                r'(\d{3,4}p)',
                r'(HD|SD|4K|1080p|720p|480p)',
                r'(BluRay|DVDRip|WEBRip|HDTV)'
            ]

            page_text = soup.get_text()
            for pattern in quality_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    return match.group(1)
        except Exception as e:
            self.logger.error(f"Failed to extract quality with BS4: {e}")

        return None

    def _extract_year_bs4(self, soup) -> Optional[str]:
        """Extract movie year using BeautifulSoup"""
        try:
            year_pattern = r'(19|20)\d{2}'
            page_text = soup.get_text()
            matches = re.findall(year_pattern, page_text)
            if matches:
                # Return the most recent year found
                years = [int(year) for year in matches if len(year) == 4]
                if years:
                    return str(max(years))
        except Exception as e:
            self.logger.error(f"Failed to extract year with BS4: {e}")

        return None

    def extract_multiple_movies(self, urls: List[str]) -> List[MovieInfo]:
        """
        Extract information from multiple movie URLs

        Args:
            urls: List of movie URLs to process

        Returns:
            List of MovieInfo objects
        """
        results = []
        total = len(urls)

        self.logger.info(f"Processing {total} movie URLs...")

        for i, url in enumerate(urls, 1):
            self.logger.info(f"Processing {i}/{total}: {url}")

            try:
                movie_info = self.extract_movie_info(url)
                if movie_info:
                    results.append(movie_info)
                    self.logger.info(f"Successfully extracted: {movie_info.title}")
                else:
                    self.logger.warning(f"Failed to extract info from: {url}")

                # Add delay between requests to be respectful
                time.sleep(2)

            except Exception as e:
                self.logger.error(f"Error processing {url}: {e}")
                continue

        self.logger.info(f"Completed processing. Successfully extracted {len(results)}/{total} movies.")
        return results

    def save_results_to_json(self, results: List[MovieInfo], filename: str = "extracted_movies.json"):
        """Save extraction results to JSON file"""
        try:
            data = []
            for movie in results:
                data.append({
                    'title': movie.title,
                    'url': movie.url,
                    'download_links': movie.download_links,
                    'streaming_links': movie.streaming_links,
                    'quality': movie.quality,
                    'language': movie.language,
                    'size': movie.size,
                    'year': movie.year
                })

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Results saved to {filename}")

        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")

    def print_results(self, results: List[MovieInfo]):
        """Print extraction results in a formatted way"""
        if not results:
            print("No results to display.")
            return

        print(f"\n{'='*80}")
        print(f"EXTRACTION RESULTS ({len(results)} movies)")
        print(f"{'='*80}")

        for i, movie in enumerate(results, 1):
            print(f"\n{i}. {movie.title}")
            print(f"   URL: {movie.url}")

            if movie.year:
                print(f"   Year: {movie.year}")
            if movie.quality:
                print(f"   Quality: {movie.quality}")

            if movie.download_links:
                print(f"   Download Links ({len(movie.download_links)}):")
                for link in movie.download_links[:5]:  # Show first 5 links
                    print(f"     - {link}")
                if len(movie.download_links) > 5:
                    print(f"     ... and {len(movie.download_links) - 5} more")

            if movie.streaming_links:
                print(f"   Streaming Links ({len(movie.streaming_links)}):")
                for link in movie.streaming_links[:3]:  # Show first 3 links
                    print(f"     - {link}")
                if len(movie.streaming_links) > 3:
                    print(f"     ... and {len(movie.streaming_links) - 3} more")

            print(f"   {'-'*60}")

    def discover_movie_urls(self, base_url: str, max_pages: int = 5) -> List[str]:
        """
        Discover movie URLs from a streaming site's catalog pages

        Args:
            base_url: Base URL of the streaming site
            max_pages: Maximum number of pages to crawl

        Returns:
            List of discovered movie URLs
        """
        movie_urls = []

        try:
            if not self._setup_requests():
                return movie_urls

            for page in range(1, max_pages + 1):
                page_url = f"{base_url}/page/{page}" if page > 1 else base_url

                try:
                    response = self.session.get(page_url, timeout=self.timeout)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Common selectors for movie links
                    movie_selectors = [
                        'a[href*="/films/"]',
                        'a[href*="/movie/"]',
                        'a[href*="/film/"]',
                        '.movie-item a',
                        '.film-item a',
                        '.post-title a'
                    ]

                    page_movies = []
                    for selector in movie_selectors:
                        links = soup.select(selector)
                        for link in links:
                            href = link.get('href')
                            if href:
                                full_url = urljoin(base_url, href)
                                if full_url not in movie_urls and full_url not in page_movies:
                                    page_movies.append(full_url)

                    movie_urls.extend(page_movies)
                    self.logger.info(f"Found {len(page_movies)} movies on page {page}")

                    # If no movies found on this page, stop crawling
                    if not page_movies:
                        break

                    time.sleep(1)  # Be respectful

                except Exception as e:
                    self.logger.error(f"Error crawling page {page}: {e}")
                    break

        except Exception as e:
            self.logger.error(f"Error in movie discovery: {e}")

        self.logger.info(f"Discovered {len(movie_urls)} total movie URLs")
        return movie_urls


def main():
    """Main CLI function"""
    import argparse

    parser = argparse.ArgumentParser(
        description="French Stream Movie Extractor - Extract download links from French streaming sites"
    )

    parser.add_argument(
        'url',
        help='Movie URL to extract or base URL for discovery'
    )

    parser.add_argument(
        '--batch',
        action='store_true',
        help='Process multiple URLs from a file (one URL per line)'
    )

    parser.add_argument(
        '--discover',
        action='store_true',
        help='Discover movie URLs from the site catalog'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        default=5,
        help='Maximum pages to crawl for discovery (default: 5)'
    )

    parser.add_argument(
        '--output',
        '-o',
        default='extracted_movies.json',
        help='Output JSON file (default: extracted_movies.json)'
    )

    parser.add_argument(
        '--headless',
        action='store_true',
        default=True,
        help='Run browser in headless mode (default: True)'
    )

    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )

    parser.add_argument(
        '--verbose',
        '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Setup logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize extractor
    extractor = FrenchStreamExtractor(
        headless=args.headless,
        timeout=args.timeout
    )

    try:
        if args.batch:
            # Process URLs from file
            try:
                with open(args.url, 'r', encoding='utf-8') as f:
                    urls = [line.strip() for line in f if line.strip()]

                print(f"Processing {len(urls)} URLs from file...")
                results = extractor.extract_multiple_movies(urls)

            except FileNotFoundError:
                print(f"Error: File '{args.url}' not found.")
                return 1

        elif args.discover:
            # Discover movie URLs from catalog
            print(f"Discovering movies from: {args.url}")
            movie_urls = extractor.discover_movie_urls(args.url, args.max_pages)

            if not movie_urls:
                print("No movie URLs discovered.")
                return 1

            print(f"Found {len(movie_urls)} movies. Starting extraction...")
            results = extractor.extract_multiple_movies(movie_urls)

        else:
            # Process single URL
            print(f"Extracting from: {args.url}")
            movie_info = extractor.extract_movie_info(args.url)

            if movie_info:
                results = [movie_info]
            else:
                print("Failed to extract movie information.")
                return 1

        # Display and save results
        if results:
            extractor.print_results(results)
            extractor.save_results_to_json(results, args.output)
            print(f"\nResults saved to: {args.output}")
        else:
            print("No results extracted.")
            return 1

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
