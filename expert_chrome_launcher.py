#!/usr/bin/env python3
"""
EXPERT CHROME LAUNCHER
Expert en: Lancement Chrome avec debugging, Automation directe
Lance Chrome avec debugging et exécute les tests automatiquement
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re
import subprocess
import os

class ExpertChromeLauncher:
    """Expert en lancement Chrome et automation"""
    
    def __init__(self):
        self.driver = None
        
    def launch_chrome_with_debugging(self):
        """Lance Chrome avec debugging activé"""
        print("🚀 Lancement Chrome avec debugging expert...")
        
        chrome_options = Options()
        
        # Options expert pour debugging et stealth
        expert_options = [
            "--remote-debugging-port=9222",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=VizDisplayCompositor",
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        for option in expert_options:
            chrome_options.add_argument(option)
        
        # Prefs expert
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts stealth expert
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'fr-FR', 'fr']});
                window.chrome = {
                    runtime: {
                        onConnect: undefined,
                        onMessage: undefined
                    }
                };
            """)
            
            # Taille réaliste
            self.driver.set_window_size(1366, 768)
            
            print("   ✅ Chrome lancé avec debugging expert")
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur lancement: {e}")
            return False
    
    def navigate_to_target(self, url):
        """Navigation experte vers le site cible"""
        print(f"🌐 Navigation experte vers: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            print(f"   ✅ Page chargée: {self.driver.title}")
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur navigation: {e}")
            return False
    
    def expert_cloudflare_handler(self):
        """Gestion experte de Cloudflare"""
        print("🛡️ Gestion experte Cloudflare...")
        
        max_wait = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                title = self.driver.title.lower()
                
                # Vérifier si on est sur une page Cloudflare
                if any(indicator in title for indicator in ["cloudflare", "checking", "moment", "instant"]):
                    print("   ⏳ Page Cloudflare détectée - Attente...")
                    
                    # Simulation d'activité humaine
                    self._simulate_human_activity()
                    time.sleep(3)
                    continue
                
                # Vérifier si on a accès au contenu
                body = self.driver.find_element(By.TAG_NAME, "body")
                if body and len(body.text) > 1000:
                    print("   ✅ Cloudflare passé - Accès au contenu!")
                    return True
                
            except Exception as e:
                print(f"   ⚠️ Erreur vérification: {e}")
            
            time.sleep(2)
        
        print("   ⚠️ Timeout Cloudflare - Continuons quand même")
        return True
    
    def _simulate_human_activity(self):
        """Simule une activité humaine légère"""
        try:
            # Petit mouvement de souris
            actions = ActionChains(self.driver)
            actions.move_by_offset(10, 10)
            actions.move_by_offset(-10, -10)
            actions.perform()
        except:
            pass
    
    def expert_uqload_finder(self):
        """Recherche experte d'UQload"""
        print("🔍 Recherche experte UQload...")
        
        try:
            # Attendre le chargement complet
            time.sleep(5)
            
            # Recherche multi-méthodes
            uqload_elements = self.driver.execute_script("""
                var elements = [];
                
                // Méthode 1: Sélecteurs directs
                var selectors = [
                    'a[href*="uqload" i]',
                    'button[onclick*="uqload" i]',
                    '[data-server*="uqload" i]',
                    '*[class*="uqload" i]',
                    '*[id*="uqload" i]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                elements.push({
                                    method: 'selector',
                                    selector: selector,
                                    text: (element.textContent || element.innerText || '').substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                // Méthode 2: Recherche par texte
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                var textNode;
                while (textNode = walker.nextNode()) {
                    var text = textNode.textContent.toLowerCase();
                    if (text.includes('uqload') || text.includes('uq load')) {
                        var parent = textNode.parentElement;
                        if (parent && parent.offsetParent !== null) {
                            var rect = parent.getBoundingClientRect();
                            if (rect.width > 10 && rect.height > 10) {
                                elements.push({
                                    method: 'text',
                                    text: textNode.textContent.substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        }
                    }
                }
                
                // Méthode 3: Recherche par attributs
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    var attrs = ['title', 'alt', 'data-title', 'aria-label'];
                    
                    for (var j = 0; j < attrs.length; j++) {
                        var attrValue = element.getAttribute(attrs[j]);
                        if (attrValue && attrValue.toLowerCase().includes('uqload')) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                elements.push({
                                    method: 'attribute',
                                    attribute: attrs[j],
                                    text: attrValue.substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                            break;
                        }
                    }
                }
                
                return elements;
            """)
            
            print(f"   📍 {len(uqload_elements)} éléments UQload trouvés")
            
            # Afficher les éléments trouvés
            for i, element in enumerate(uqload_elements[:5]):
                method = element['method']
                text = element['text'].strip()
                pos = element['position']
                print(f"   {i+1}. [{method}] '{text}' à ({pos['x']}, {pos['y']})")
            
            return uqload_elements
            
        except Exception as e:
            print(f"   ❌ Erreur recherche UQload: {e}")
            return []
    
    def expert_click_uqload(self, uqload_elements):
        """Clic expert sur UQload"""
        if not uqload_elements:
            print("❌ Aucun élément UQload à cliquer")
            return False
        
        print("🖱️ Clic expert sur UQload...")
        
        try:
            # Prendre le premier élément trouvé
            element = uqload_elements[0]
            x, y = element['position']['x'], element['position']['y']
            text = element['text'].strip()
            
            print(f"   🎯 Clic sur '{text}' à ({x}, {y})")
            
            # Méthode 1: Clic JavaScript
            success = self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                    return true;
                }}
                return false;
            """)
            
            if success:
                print("   ✅ Clic UQload réussi!")
                time.sleep(5)
                return True
            else:
                print("   ❌ Élément non trouvé aux coordonnées")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur clic UQload: {e}")
            return False
    
    def expert_find_play_button(self):
        """Recherche experte du bouton play"""
        print("▶️ Recherche experte bouton play...")
        
        try:
            time.sleep(5)
            
            play_buttons = self.driver.execute_script("""
                var buttons = [];
                
                // Recherche exhaustive de boutons play
                var selectors = [
                    '.play-button',
                    '.video-play-button',
                    '[class*="play"]',
                    'button[class*="play"]',
                    '[onclick*="play"]',
                    '.vjs-big-play-button',
                    '[title*="play" i]',
                    '[alt*="play" i]',
                    '.play',
                    '#play',
                    '[data-action="play"]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                if (rect.width > 10 && rect.height > 10) {
                                    buttons.push({
                                        selector: selector,
                                        text: (element.textContent || element.title || element.alt || '').substring(0, 30),
                                        position: {
                                            x: Math.round(rect.left + rect.width / 2),
                                            y: Math.round(rect.top + rect.height / 2)
                                        }
                                    });
                                }
                            }
                        });
                    } catch(e) {}
                });
                
                // Si pas de bouton spécifique, chercher au centre de l'écran
                if (buttons.length === 0) {
                    buttons.push({
                        selector: 'center_screen',
                        text: 'Centre écran (position standard)',
                        position: {
                            x: Math.round(window.innerWidth / 2),
                            y: Math.round(window.innerHeight / 2)
                        }
                    });
                }
                
                return buttons;
            """)
            
            print(f"   ▶️ {len(play_buttons)} boutons play trouvés")
            
            for i, button in enumerate(play_buttons[:3]):
                text = button['text'].strip()
                pos = button['position']
                print(f"   {i+1}. '{text}' à ({pos['x']}, {pos['y']})")
            
            return play_buttons
            
        except Exception as e:
            print(f"   ❌ Erreur recherche play: {e}")
            return []
    
    def expert_click_play(self, play_buttons):
        """Clic expert sur play"""
        if not play_buttons:
            print("❌ Aucun bouton play à cliquer")
            return False
        
        print("▶️ Clic expert sur play...")
        
        try:
            # Essayer tous les boutons trouvés
            for i, button in enumerate(play_buttons[:3]):
                x, y = button['position']['x'], button['position']['y']
                text = button['text'].strip()
                
                print(f"   🎯 Tentative {i+1}: '{text}' à ({x}, {y})")
                
                success = self.driver.execute_script(f"""
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.click();
                        return true;
                    }}
                    return false;
                """)
                
                if success:
                    print(f"   ✅ Clic play réussi!")
                    time.sleep(8)
                    return True
                else:
                    print(f"   ⚠️ Pas d'élément à ({x}, {y})")
            
            print("   ❌ Aucun clic play réussi")
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur clic play: {e}")
            return False
    
    def expert_extract_videos(self):
        """Extraction experte des vidéos"""
        print("🎥 Extraction experte des vidéos...")
        
        try:
            video_urls = []
            
            # Méthode 1: Éléments video directs
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append({
                        'url': src,
                        'method': 'video_element',
                        'type': 'direct'
                    })
                    print(f"   📹 Video element: {src[:60]}...")
            
            # Méthode 2: Analyse du code source
            page_source = self.driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'"url":\s*"([^"]+\.(?:mp4|m3u8|webm|avi|mkv)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm|avi|mkv)(?:\?[^"\s]*)?',
                r'https?://[^"\s]*(?:video|stream|play)[^"\s]*\.(?:mp4|m3u8|webm)'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and not any(v['url'] == match for v in video_urls):
                        video_urls.append({
                            'url': match,
                            'method': 'regex',
                            'type': 'extracted'
                        })
                        print(f"   📹 Regex match: {match[:60]}...")
            
            # Méthode 3: JavaScript avancé
            js_videos = self.driver.execute_script("""
                var videos = [];
                
                // Chercher dans les variables globales
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string' && 
                            (value.includes('.mp4') || value.includes('.m3u8') || value.includes('.webm')) &&
                            value.startsWith('http')) {
                            videos.push(value);
                        }
                    } catch(e) {}
                }
                
                // Chercher dans les éléments avec data-src
                var elements = document.querySelectorAll('[data-src], [data-url], [data-file]');
                elements.forEach(function(element) {
                    ['data-src', 'data-url', 'data-file'].forEach(function(attr) {
                        var value = element.getAttribute(attr);
                        if (value && value.startsWith('http') && 
                            (value.includes('.mp4') || value.includes('.m3u8') || value.includes('.webm'))) {
                            videos.push(value);
                        }
                    });
                });
                
                return videos;
            """)
            
            for js_video in js_videos:
                if not any(v['url'] == js_video for v in video_urls):
                    video_urls.append({
                        'url': js_video,
                        'method': 'javascript',
                        'type': 'extracted'
                    })
                    print(f"   📹 JavaScript: {js_video[:60]}...")
            
            print(f"\n🎬 {len(video_urls)} liens vidéo trouvés au total")
            
            return video_urls
            
        except Exception as e:
            print(f"   ❌ Erreur extraction vidéo: {e}")
            return []
    
    def save_results(self, video_urls):
        """Sauvegarde les résultats"""
        if not video_urls:
            print("❌ Aucun résultat à sauvegarder")
            return
        
        result = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'url': self.driver.current_url,
            'video_count': len(video_urls),
            'videos': video_urls,
            'primary_link': video_urls[0]['url'] if video_urls else None,
            'extraction_method': 'expert_chrome_launcher',
            'success': True
        }
        
        with open('expert_chrome_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultats sauvegardés: expert_chrome_results.json")
        print(f"🎯 LIEN PRINCIPAL: {result['primary_link']}")
        
        # Afficher tous les liens
        print(f"\n🎬 TOUS LES LIENS VIDÉO ({len(video_urls)}):")
        for i, video in enumerate(video_urls, 1):
            method = video['method']
            url = video['url']
            print(f"{i}. [{method}] {url}")
    
    def run_expert_extraction(self, url):
        """Lance l'extraction experte complète"""
        print("🎓 EXPERT CHROME LAUNCHER - EXTRACTION COMPLÈTE")
        print("=" * 70)
        print("👨‍💻 Expert en: Chrome automation, UQload extraction")
        print(f"🔗 URL cible: {url}")
        
        try:
            # Étape 1: Lancer Chrome
            if not self.launch_chrome_with_debugging():
                return False
            
            # Étape 2: Navigation
            if not self.navigate_to_target(url):
                return False
            
            # Étape 3: Gestion Cloudflare
            self.expert_cloudflare_handler()
            
            # Étape 4: Recherche UQload
            uqload_elements = self.expert_uqload_finder()
            
            # Étape 5: Clic UQload
            if uqload_elements:
                if self.expert_click_uqload(uqload_elements):
                    # Étape 6: Recherche et clic play
                    play_buttons = self.expert_find_play_button()
                    self.expert_click_play(play_buttons)
            
            # Étape 7: Extraction vidéos
            video_urls = self.expert_extract_videos()
            
            # Étape 8: Sauvegarde
            if video_urls:
                self.save_results(video_urls)
                print("\n🎉 EXTRACTION EXPERTE RÉUSSIE!")
                return True
            else:
                print("\n❌ Aucune vidéo trouvée")
                return False
                
        except Exception as e:
            print(f"❌ Erreur extraction experte: {e}")
            return False
        
        finally:
            if self.driver:
                print("\n⏳ Navigateur reste ouvert pour inspection...")
                input("Appuyez sur Entrée pour fermer...")
                self.driver.quit()

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎓 EXPERT CHROME LAUNCHER")
    print("=" * 50)
    print("🚀 Lance Chrome avec debugging et exécute l'extraction")
    print("🎯 Méthodes expertes: Multi-recherche, Clics précis, Extraction avancée")
    
    launcher = ExpertChromeLauncher()
    success = launcher.run_expert_extraction(url)
    
    if success:
        print("\n🏆 MISSION EXPERTE ACCOMPLIE!")
    else:
        print("\n❌ Mission experte échouée")

if __name__ == "__main__":
    main()
