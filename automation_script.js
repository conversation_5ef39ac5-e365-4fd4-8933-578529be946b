
// SCRIPT D'AUTOMATION JAVASCRIPT EXPERT
console.log('🚀 AUTOMATION JAVASCRIPT DÉMARRÉE');

// Fonction d'attente
function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction de recherche UQload
function findUQload() {
    console.log('🔍 Recherche UQload...');
    
    // Méthode 1: Par texte
    const textElements = Array.from(document.querySelectorAll('*')).filter(el => 
        el.textContent && el.textContent.toLowerCase().includes('uqload')
    );
    
    for (let element of textElements) {
        if (element.offsetParent !== null) { // Visible
            console.log('🎯 UQload trouvé par texte:', element.textContent);
            element.click();
            return true;
        }
    }
    
    // Méthode 2: Par liens
    const links = document.querySelectorAll('a[href*="uqload" i]');
    for (let link of links) {
        if (link.offsetParent !== null) {
            console.log('🎯 UQload trouvé par lien:', link.href);
            link.click();
            return true;
        }
    }
    
    console.log('❌ UQload non trouvé');
    return false;
}

// Fonction de recherche play
function findPlay() {
    console.log('▶️ Recherche bouton play...');
    
    const playSelectors = [
        '.play-button', '.video-play-button', '[class*="play"]',
        'button[class*="play"]', '[onclick*="play"]', '.vjs-big-play-button'
    ];
    
    for (let selector of playSelectors) {
        const elements = document.querySelectorAll(selector);
        for (let element of elements) {
            if (element.offsetParent !== null) {
                console.log('▶️ Play trouvé:', selector);
                element.click();
                return true;
            }
        }
    }
    
    // Clic centre écran
    console.log('▶️ Clic centre écran...');
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    
    const element = document.elementFromPoint(centerX, centerY);
    if (element) {
        element.click();
        return true;
    }
    
    return false;
}

// Fonction d'extraction vidéo
function extractVideos() {
    console.log('🎥 Extraction vidéos...');
    const videos = [];
    
    // Méthode 1: Éléments video
    document.querySelectorAll('video').forEach(video => {
        if (video.src && video.src.startsWith('http')) {
            videos.push(video.src);
            console.log('📹 Video src:', video.src);
        }
        
        video.querySelectorAll('source').forEach(source => {
            if (source.src && source.src.startsWith('http')) {
                videos.push(source.src);
                console.log('📹 Video source:', source.src);
            }
        });
    });
    
    // Méthode 2: Regex dans le code source
    const patterns = [
        /"file":\s*"([^"]+\.mp4[^"]*)"/gi,
        /"src":\s*"([^"]+\.mp4[^"]*)"/gi,
        /https?:\/\/[^"\s]+\.mp4(?:\?[^"\s]*)?/gi
    ];
    
    const pageSource = document.documentElement.outerHTML;
    
    patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(pageSource)) !== null) {
            const url = match[1] || match[0];
            if (url && url.startsWith('http') && !videos.includes(url)) {
                videos.push(url);
                console.log('📹 Regex:', url);
            }
        }
    });
    
    return [...new Set(videos)]; // Supprimer doublons
}

// Fonction principale d'automation
async function runAutomation() {
    console.log('🎯 AUTOMATION PRINCIPALE');
    
    try {
        // Attendre Cloudflare
        console.log('⏳ Attente Cloudflare...');
        let waited = 0;
        while (waited < 30000) { // 30 secondes max
            if (!document.title.toLowerCase().includes('cloudflare') && 
                !document.title.toLowerCase().includes('checking') &&
                document.body.innerText.length > 1000) {
                console.log('✅ Cloudflare passé');
                break;
            }
            await wait(2000);
            waited += 2000;
        }
        
        // Rechercher et cliquer UQload
        const uqloadFound = findUQload();
        if (uqloadFound) {
            await wait(5000); // Attendre chargement
            
            // Rechercher et cliquer play
            findPlay();
            await wait(8000); // Attendre chargement vidéo
        }
        
        // Extraire vidéos
        const videos = extractVideos();
        
        if (videos.length > 0) {
            console.log('🎉 EXTRACTION RÉUSSIE!');
            console.log('🎯 LIEN PRINCIPAL:', videos[0]);
            
            // Sauvegarder dans localStorage pour récupération
            const result = {
                timestamp: new Date().toISOString(),
                videos: videos,
                primary: videos[0],
                success: true
            };
            
            localStorage.setItem('automation_result', JSON.stringify(result));
            console.log('💾 Résultats sauvegardés dans localStorage');
            
            return result;
        } else {
            console.log('❌ Aucune vidéo trouvée');
            return null;
        }
        
    } catch (error) {
        console.error('❌ Erreur automation:', error);
        return null;
    }
}

// Lancer l'automation
runAutomation().then(result => {
    if (result) {
        console.log('🏆 AUTOMATION TERMINÉE AVEC SUCCÈS');
    } else {
        console.log('❌ Automation échouée');
    }
});
