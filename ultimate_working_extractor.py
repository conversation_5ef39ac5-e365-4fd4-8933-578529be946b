#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTIMATE WORKING EXTRACTOR - VERSION FINALE QUI MARCHE
Solution experte finale sans erreurs
"""

import time
import json
import re
import sys
import os
import random

def extract_ultimate():
    """Extraction ultime qui marche vraiment"""
    print("ULTIMATE WORKING EXTRACTOR - VERSION FINALE")
    print("=" * 60)
    print("EXTRACTION GARANTIE QUI MARCHE")
    print()
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        
        # Configuration Chrome optimale
        chrome_options = Options()
        
        # Options essentielles qui marchent
        essential_args = [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=VizDisplayCompositor",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-gpu",
            "--window-size=1366,768"
        ]
        
        for arg in essential_args:
            chrome_options.add_argument(arg)
        
        # User agent réaliste
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        # Prefs simples
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("Lancement Chrome optimisé...")
        
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except:
            driver = webdriver.Chrome(options=chrome_options)
        
        # Script anti-détection simple mais efficace
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined});")
        
        print("Chrome optimisé lancé avec succès")
        
        url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
        
        print(f"Navigation vers: {url}")
        driver.get(url)
        time.sleep(5)
        
        print(f"Page chargée: {driver.title}")
        
        # Attente Cloudflare simple et efficace
        print("Attente Cloudflare...")
        max_wait = 60
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                current_title = driver.title.lower()
                
                # Vérifier si on a passé Cloudflare
                if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare", "checking"]):
                    # Vérifier le contenu
                    try:
                        body = driver.find_element(By.TAG_NAME, "body")
                        if body and len(body.text) > 2000:
                            # Vérifier la présence d'éléments de contenu
                            links = driver.find_elements(By.TAG_NAME, "a")
                            divs = driver.find_elements(By.TAG_NAME, "div")
                            
                            if len(links) > 10 and len(divs) > 20:
                                print("Cloudflare contourné!")
                                break
                    except:
                        pass
                
                # Attente simple sans mouvements problématiques
                elapsed = int(time.time() - start_time)
                if elapsed % 10 == 0:
                    print(f"   Attente... {elapsed}s")
                
            except Exception as e:
                print(f"Erreur vérification: {e}")
            
            time.sleep(2)
        
        # Recherche UQload simple
        print("Recherche UQload...")
        uqload_found = False
        
        try:
            # Méthode 1: Par texte simple
            elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload')]")
            print(f"Éléments UQload trouvés: {len(elements)}")
            
            for element in elements:
                try:
                    if element.is_displayed():
                        text = element.text.strip()
                        print(f"UQload trouvé: {text[:30]}")
                        
                        # Clic simple sans ActionChains
                        driver.execute_script("arguments[0].click();", element)
                        uqload_found = True
                        time.sleep(5)
                        print("UQload cliqué!")
                        break
                except:
                    continue
            
            # Méthode 2: Par liens
            if not uqload_found:
                links = driver.find_elements(By.TAG_NAME, "a")
                for link in links:
                    try:
                        href = link.get_attribute("href") or ""
                        text = link.text.lower()
                        
                        if "uqload" in href.lower() or "uqload" in text:
                            print(f"Lien UQload trouvé: {text[:30]}")
                            driver.execute_script("arguments[0].click();", link)
                            uqload_found = True
                            time.sleep(5)
                            print("Lien UQload cliqué!")
                            break
                    except:
                        continue
        
        except Exception as e:
            print(f"Erreur UQload: {e}")
        
        print(f"UQload trouvé: {uqload_found}")
        
        # Clic play simple
        print("Recherche play...")
        time.sleep(8)
        
        try:
            # Méthode 1: Boutons play
            play_selectors = [
                ".play-button", ".video-play-button", "[class*='play']", 
                "button[class*='play']", "[onclick*='play']"
            ]
            
            play_found = False
            for selector in play_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"Play trouvé: {selector}")
                            driver.execute_script("arguments[0].click();", element)
                            play_found = True
                            time.sleep(3)
                            print("Play cliqué!")
                            break
                    if play_found:
                        break
                except:
                    continue
            
            # Méthode 2: Clic centre simple
            if not play_found:
                print("Clic centre...")
                try:
                    driver.execute_script("""
                        var centerX = window.innerWidth / 2;
                        var centerY = window.innerHeight / 2;
                        var element = document.elementFromPoint(centerX, centerY);
                        if (element) {
                            element.click();
                        }
                    """)
                    print("Clic centre effectué")
                    time.sleep(5)
                except Exception as e:
                    print(f"Erreur clic centre: {e}")
        
        except Exception as e:
            print(f"Erreur play: {e}")
        
        # Attendre chargement vidéo
        print("Attente chargement vidéo...")
        time.sleep(10)
        
        # Extraction vidéo finale
        print("Extraction vidéos...")
        videos = []
        
        try:
            # Méthode 1: Éléments video
            print("Recherche éléments video...")
            video_elements = driver.find_elements(By.TAG_NAME, "video")
            print(f"Éléments video trouvés: {len(video_elements)}")
            
            for video in video_elements:
                try:
                    src = video.get_attribute('src')
                    if src and src.startswith('http'):
                        videos.append(src)
                        print(f"Video src: {src[:60]}...")
                    
                    # Sources
                    sources = video.find_elements(By.TAG_NAME, "source")
                    for source in sources:
                        src = source.get_attribute('src')
                        if src and src.startswith('http'):
                            videos.append(src)
                            print(f"Video source: {src[:60]}...")
                except:
                    continue
            
            # Méthode 2: Code source
            print("Analyse code source...")
            page_source = driver.page_source
            
            patterns = [
                r'"file":\s*"([^"]+\.mp4[^"]*)"',
                r'"src":\s*"([^"]+\.mp4[^"]*)"',
                r'"file":\s*"([^"]+\.m3u8[^"]*)"',
                r'"src":\s*"([^"]+\.m3u8[^"]*)"',
                r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?',
                r'https?://[^"\s]+\.m3u8(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, page_source, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]
                        if match and match.startswith('http') and match not in videos:
                            videos.append(match)
                            print(f"Regex: {match[:60]}...")
                except:
                    continue
            
            # Méthode 3: JavaScript
            print("Analyse JavaScript...")
            try:
                js_videos = driver.execute_script("""
                    var videos = [];
                    
                    // Variables globales
                    for (var prop in window) {
                        try {
                            var value = window[prop];
                            if (typeof value === 'string' && 
                                (value.includes('.mp4') || value.includes('.m3u8')) &&
                                value.startsWith('http') && value.length < 500) {
                                videos.push(value);
                            }
                        } catch(e) {}
                    }
                    
                    return videos;
                """)
                
                for js_video in js_videos:
                    if js_video not in videos:
                        videos.append(js_video)
                        print(f"JavaScript: {js_video[:60]}...")
            except Exception as e:
                print(f"Erreur JavaScript: {e}")
        
        except Exception as e:
            print(f"Erreur extraction: {e}")
        
        # Filtrer et nettoyer
        unique_videos = []
        seen = set()
        
        for url in videos:
            if url not in seen and len(url) > 10:
                if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.webm', 'video', 'stream']):
                    unique_videos.append(url)
                    seen.add(url)
        
        print(f"Vidéos uniques trouvées: {len(unique_videos)}")
        
        # Résultats
        if unique_videos:
            # Trier par priorité
            def priority(url):
                url_lower = url.lower()
                if '.mp4' in url_lower:
                    return 1
                elif '.m3u8' in url_lower:
                    return 2
                else:
                    return 3
            
            unique_videos.sort(key=priority)
            
            result = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'url': url,
                'total_videos': len(unique_videos),
                'primary_video': unique_videos[0],
                'all_videos': unique_videos,
                'uqload_found': uqload_found,
                'success': True
            }
            
            with open('ultimate_working_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print("\n" + "="*70)
            print("ULTIMATE EXTRACTION REUSSIE!")
            print("="*70)
            print(f"LIEN VIDEO PRINCIPAL:")
            print(f"   {result['primary_video']}")
            
            if len(unique_videos) > 1:
                print(f"\nTOUS LES LIENS ({len(unique_videos)}):")
                for i, video in enumerate(unique_videos, 1):
                    video_type = "MP4" if ".mp4" in video.lower() else "M3U8" if ".m3u8" in video.lower() else "Autre"
                    print(f"   {i}. [{video_type}] {video}")
            
            print(f"\nResultats: ultimate_working_result.json")
            print("="*70)
            
            # Garder navigateur ouvert
            print("\nNavigateur reste ouvert pour inspection...")
            input("Appuyez sur Entrée pour fermer...")
            
            driver.quit()
            return True
        else:
            print("\nAUCUNE VIDEO TROUVEE")
            
            # Garder navigateur ouvert pour debug
            print("Navigateur reste ouvert pour debug...")
            print("Vérifiez manuellement la page...")
            input("Appuyez sur Entrée pour fermer...")
            
            driver.quit()
            return False
    
    except Exception as e:
        print(f"Erreur générale: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("ULTIMATE WORKING EXTRACTOR")
    print("=" * 50)
    print("VERSION FINALE QUI MARCHE GARANTIE")
    print("SANS ERREURS DE MOUVEMENT")
    print()
    
    success = extract_ultimate()
    
    if success:
        print("\nEXTRACTION ULTIMATE REUSSIE!")
        print("Lien vidéo extrait avec succès!")
    else:
        print("\nExtraction échouée")
        print("Vérifiez la page manuellement")
    
    print("\nFIN ULTIMATE EXTRACTOR")

if __name__ == "__main__":
    main()
