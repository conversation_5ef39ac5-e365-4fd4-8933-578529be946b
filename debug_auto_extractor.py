#!/usr/bin/env python3
"""
DEBUG AUTO EXTRACTOR - VERSION DEBUG
Affiche chaque étape en temps réel pour diagnostiquer
"""

import sys
import time

print("🚀 DEBUG AUTO EXTRACTOR - DÉMARRAGE")
print("=" * 50)
print("🔍 Version debug avec affichage temps réel")
print()

# Forcer l'affichage immédiat
sys.stdout.flush()

print("📋 Étape 1: Test des imports...")
sys.stdout.flush()

try:
    print("   🔍 Import selenium...")
    sys.stdout.flush()
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    print("   ✅ Selenium importé")
    sys.stdout.flush()
    
    print("   🔍 Import webdriver_manager...")
    sys.stdout.flush()
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    print("   ✅ WebDriver Manager importé")
    sys.stdout.flush()
    
    print("   🔍 Import autres modules...")
    sys.stdout.flush()
    import json
    import re
    print("   ✅ Tous les modules importés")
    sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur import: {e}")
    sys.stdout.flush()
    sys.exit(1)

print("\n📋 Étape 2: Configuration Chrome...")
sys.stdout.flush()

try:
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    print("   ✅ Options Chrome configurées")
    sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur configuration: {e}")
    sys.stdout.flush()
    sys.exit(1)

print("\n📋 Étape 3: Téléchargement ChromeDriver...")
sys.stdout.flush()

try:
    print("   ⏳ Téléchargement en cours...")
    sys.stdout.flush()
    
    driver_path = ChromeDriverManager().install()
    print(f"   ✅ ChromeDriver téléchargé: {driver_path}")
    sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur téléchargement: {e}")
    sys.stdout.flush()
    print("   💡 Essai sans téléchargement...")
    sys.stdout.flush()
    driver_path = None

print("\n📋 Étape 4: Lancement Chrome...")
sys.stdout.flush()

driver = None
try:
    if driver_path:
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
    else:
        driver = webdriver.Chrome(options=chrome_options)
    
    print("   ✅ Chrome lancé avec succès!")
    sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur lancement Chrome: {e}")
    sys.stdout.flush()
    sys.exit(1)

print("\n📋 Étape 5: Navigation...")
sys.stdout.flush()

url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"

try:
    print(f"   🌐 Navigation vers: {url}")
    sys.stdout.flush()
    
    driver.get(url)
    time.sleep(3)
    
    title = driver.title
    print(f"   ✅ Page chargée: {title}")
    sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur navigation: {e}")
    sys.stdout.flush()

print("\n📋 Étape 6: Attente Cloudflare...")
sys.stdout.flush()

try:
    max_wait = 20
    waited = 0
    
    while waited < max_wait:
        try:
            current_title = driver.title.lower()
            
            if any(word in current_title for word in ["cloudflare", "checking", "moment"]):
                print(f"   ⏳ Cloudflare détecté - Attente {waited}s")
                sys.stdout.flush()
                time.sleep(2)
                waited += 2
                continue
            
            body = driver.find_element(By.TAG_NAME, "body")
            if len(body.text) > 1000:
                print("   ✅ Contenu accessible!")
                sys.stdout.flush()
                break
                
        except:
            pass
        
        time.sleep(2)
        waited += 2
    
    if waited >= max_wait:
        print("   ⚠️ Timeout Cloudflare")
        sys.stdout.flush()
        
except Exception as e:
    print(f"   ❌ Erreur Cloudflare: {e}")
    sys.stdout.flush()

print("\n📋 Étape 7: Recherche UQload...")
sys.stdout.flush()

uqload_found = False
try:
    elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload')]")
    print(f"   🔍 {len(elements)} éléments UQload potentiels trouvés")
    sys.stdout.flush()
    
    for i, element in enumerate(elements):
        try:
            if element.is_displayed():
                text = element.text.strip()
                print(f"   🎯 UQload {i+1}: '{text}'")
                sys.stdout.flush()
                
                element.click()
                print(f"   ✅ UQload cliqué!")
                sys.stdout.flush()
                uqload_found = True
                time.sleep(5)
                break
        except:
            continue
    
    if not uqload_found:
        print("   ❌ UQload non trouvé")
        sys.stdout.flush()
        
except Exception as e:
    print(f"   ❌ Erreur UQload: {e}")
    sys.stdout.flush()

print("\n📋 Étape 8: Recherche Play...")
sys.stdout.flush()

try:
    time.sleep(5)
    
    # Clic centre écran
    center_x = driver.execute_script("return window.innerWidth / 2;")
    center_y = driver.execute_script("return window.innerHeight / 2;")
    
    print(f"   ▶️ Clic centre écran ({center_x}, {center_y})")
    sys.stdout.flush()
    
    driver.execute_script(f"""
        var element = document.elementFromPoint({center_x}, {center_y});
        if (element) element.click();
    """)
    
    print("   ✅ Clic play effectué")
    sys.stdout.flush()
    time.sleep(8)
    
except Exception as e:
    print(f"   ❌ Erreur play: {e}")
    sys.stdout.flush()

print("\n📋 Étape 9: Extraction vidéos...")
sys.stdout.flush()

video_urls = []

try:
    # Méthode 1: Éléments video
    print("   📹 Recherche éléments video...")
    sys.stdout.flush()
    
    videos = driver.find_elements(By.TAG_NAME, "video")
    print(f"   🔍 {len(videos)} éléments video trouvés")
    sys.stdout.flush()
    
    for video in videos:
        src = video.get_attribute('src')
        if src and src.startswith('http'):
            video_urls.append(src)
            print(f"   📹 Video: {src[:50]}...")
            sys.stdout.flush()
    
    # Méthode 2: Regex
    print("   🔍 Analyse code source...")
    sys.stdout.flush()
    
    page_source = driver.page_source
    patterns = [
        r'"file":\s*"([^"]+\.mp4[^"]*)"',
        r'https?://[^"\s]+\.mp4(?:\?[^"\s]*)?'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, page_source, re.IGNORECASE)
        for match in matches:
            if match and match.startswith('http') and match not in video_urls:
                video_urls.append(match)
                print(f"   📹 Regex: {match[:50]}...")
                sys.stdout.flush()
    
except Exception as e:
    print(f"   ❌ Erreur extraction: {e}")
    sys.stdout.flush()

print("\n📋 Étape 10: Résultats...")
sys.stdout.flush()

if video_urls:
    print("🎉 EXTRACTION DEBUG RÉUSSIE!")
    print("=" * 40)
    
    print(f"🎯 LIEN PRINCIPAL:")
    print(f"   {video_urls[0]}")
    
    if len(video_urls) > 1:
        print(f"\n📹 TOUS LES LIENS ({len(video_urls)}):")
        for i, url in enumerate(video_urls, 1):
            print(f"   {i}. {url}")
    
    # Sauvegarde
    result = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'video_links': video_urls,
        'primary_link': video_urls[0],
        'uqload_found': uqload_found,
        'success': True
    }
    
    with open('debug_auto_results.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Résultats: debug_auto_results.json")
    sys.stdout.flush()
    
else:
    print("❌ AUCUNE VIDÉO TROUVÉE")
    sys.stdout.flush()

print("\n📋 Étape 11: Fermeture...")
sys.stdout.flush()

if driver:
    print("⏳ Fermeture dans 10 secondes...")
    sys.stdout.flush()
    time.sleep(10)
    
    try:
        driver.quit()
        print("✅ Navigateur fermé")
        sys.stdout.flush()
    except:
        print("⚠️ Erreur fermeture")
        sys.stdout.flush()

print("\n🔚 FIN DEBUG")
sys.stdout.flush()
