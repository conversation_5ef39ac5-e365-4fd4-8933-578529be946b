#!/usr/bin/env python3
"""
ANALYSEUR EXPERT - CALCUL PRÉCIS DES COORDONNÉES CLOUDFLARE
Expert en analyse DOM, CSS, et positionnement web
Calcule les coordonnées EXACTES de la case à cocher
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import base64
from PIL import Image
import io

class ExpertPageAnalyzer:
    """Analyseur expert pour calcul précis des coordonnées"""
    
    def __init__(self):
        self.driver = None
        self.analysis_results = {}
        
    def setup_analysis_driver(self):
        """Configure un driver d'analyse expert"""
        print("🔬 Configuration du navigateur d'ANALYSE EXPERT...")
        
        chrome_options = Options()
        # Mode visible pour analyse visuelle
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        
        # Taille EXACTE pour calculs précis
        chrome_options.add_argument("--window-size=1366,768")
        chrome_options.add_argument("--force-device-scale-factor=1")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Scripts d'analyse
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Taille FIXE pour calculs
            self.driver.set_window_size(1366, 768)
            
            print("✅ Navigateur d'analyse configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def analyze_cloudflare_page(self, url):
        """Analyse complète de la page Cloudflare"""
        print("🔬 ANALYSE EXPERT DE LA PAGE CLOUDFLARE")
        print("=" * 70)
        print(f"🔗 URL: {url}")
        
        try:
            if not self.setup_analysis_driver():
                return None
            
            # Charger la page
            print("📡 Chargement pour analyse...")
            self.driver.get(url)
            time.sleep(8)  # Attendre le chargement complet
            
            # Analyse 1: Structure DOM complète
            print("\n🔍 ANALYSE 1: Structure DOM")
            dom_analysis = self._analyze_dom_structure()
            
            # Analyse 2: Éléments Cloudflare spécifiques
            print("\n🛡️ ANALYSE 2: Éléments Cloudflare")
            cloudflare_analysis = self._analyze_cloudflare_elements()
            
            # Analyse 3: Analyse CSS et positionnement
            print("\n🎨 ANALYSE 3: CSS et Positionnement")
            css_analysis = self._analyze_css_positioning()
            
            # Analyse 4: Capture d'écran et analyse visuelle
            print("\n📸 ANALYSE 4: Analyse Visuelle")
            visual_analysis = self._analyze_visual_elements()
            
            # Analyse 5: Calcul des coordonnées précises
            print("\n🎯 ANALYSE 5: Calcul Coordonnées Précises")
            coordinates = self._calculate_precise_coordinates()
            
            # Compilation des résultats
            self.analysis_results = {
                'url': url,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'window_size': {'width': 1366, 'height': 768},
                'dom_analysis': dom_analysis,
                'cloudflare_analysis': cloudflare_analysis,
                'css_analysis': css_analysis,
                'visual_analysis': visual_analysis,
                'calculated_coordinates': coordinates
            }
            
            # Sauvegarder l'analyse
            with open('expert_analysis_results.json', 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Analyse sauvegardée: expert_analysis_results.json")
            
            return self.analysis_results
            
        except Exception as e:
            print(f"❌ Erreur analyse: {e}")
            return None
        
        finally:
            if self.driver:
                print("\n⏳ Navigateur reste ouvert pour inspection...")
                input("Appuyez sur Entrée pour fermer...")
                self.driver.quit()
    
    def _analyze_dom_structure(self):
        """Analyse la structure DOM complète"""
        try:
            # Obtenir toute la structure HTML
            html_structure = self.driver.execute_script("""
                function analyzeElement(element, depth = 0) {
                    if (depth > 10) return null; // Limite de profondeur
                    
                    var info = {
                        tagName: element.tagName,
                        id: element.id || '',
                        className: element.className || '',
                        textContent: (element.textContent || '').substring(0, 100),
                        attributes: {},
                        position: {},
                        children: []
                    };
                    
                    // Attributs
                    for (var i = 0; i < element.attributes.length; i++) {
                        var attr = element.attributes[i];
                        info.attributes[attr.name] = attr.value;
                    }
                    
                    // Position
                    try {
                        var rect = element.getBoundingClientRect();
                        info.position = {
                            x: Math.round(rect.left),
                            y: Math.round(rect.top),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height),
                            centerX: Math.round(rect.left + rect.width / 2),
                            centerY: Math.round(rect.top + rect.height / 2)
                        };
                    } catch(e) {}
                    
                    // Enfants (limités)
                    if (depth < 3) {
                        for (var j = 0; j < Math.min(element.children.length, 10); j++) {
                            var childInfo = analyzeElement(element.children[j], depth + 1);
                            if (childInfo) info.children.push(childInfo);
                        }
                    }
                    
                    return info;
                }
                
                return analyzeElement(document.body);
            """)
            
            print("   ✅ Structure DOM analysée")
            return html_structure
            
        except Exception as e:
            print(f"   ❌ Erreur DOM: {e}")
            return {}
    
    def _analyze_cloudflare_elements(self):
        """Analyse spécifique des éléments Cloudflare"""
        try:
            cloudflare_elements = self.driver.execute_script("""
                var elements = [];
                
                // Recherche d'éléments Cloudflare
                var selectors = [
                    'input[type="checkbox"]',
                    '.cf-turnstile',
                    '#cf-turnstile',
                    '[data-sitekey]',
                    '.challenge-form',
                    'iframe',
                    '[class*="cloudflare"]',
                    '[id*="cloudflare"]',
                    '[class*="captcha"]',
                    '[id*="captcha"]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            var rect = element.getBoundingClientRect();
                            elements.push({
                                selector: selector,
                                tagName: element.tagName,
                                id: element.id || '',
                                className: element.className || '',
                                position: {
                                    x: Math.round(rect.left),
                                    y: Math.round(rect.top),
                                    width: Math.round(rect.width),
                                    height: Math.round(rect.height),
                                    centerX: Math.round(rect.left + rect.width / 2),
                                    centerY: Math.round(rect.top + rect.height / 2)
                                },
                                visible: element.offsetParent !== null,
                                attributes: {}
                            });
                            
                            // Attributs
                            for (var i = 0; i < element.attributes.length; i++) {
                                var attr = element.attributes[i];
                                elements[elements.length - 1].attributes[attr.name] = attr.value;
                            }
                        });
                    } catch(e) {}
                });
                
                return elements;
            """)
            
            print(f"   ✅ {len(cloudflare_elements)} éléments Cloudflare trouvés")
            
            # Afficher les éléments trouvés
            for i, element in enumerate(cloudflare_elements):
                pos = element['position']
                print(f"   📍 Élément {i+1}: {element['tagName']} à ({pos['centerX']}, {pos['centerY']})")
            
            return cloudflare_elements
            
        except Exception as e:
            print(f"   ❌ Erreur Cloudflare: {e}")
            return []
    
    def _analyze_css_positioning(self):
        """Analyse CSS et positionnement"""
        try:
            css_info = self.driver.execute_script("""
                var info = {
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    scroll: {
                        x: window.scrollX,
                        y: window.scrollY
                    },
                    elements_with_fixed_position: [],
                    elements_with_absolute_position: []
                };
                
                // Chercher les éléments avec position fixe/absolue
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    var style = window.getComputedStyle(element);
                    
                    if (style.position === 'fixed' || style.position === 'absolute') {
                        var rect = element.getBoundingClientRect();
                        var elementInfo = {
                            tagName: element.tagName,
                            position: style.position,
                            top: style.top,
                            left: style.left,
                            zIndex: style.zIndex,
                            rect: {
                                x: Math.round(rect.left),
                                y: Math.round(rect.top),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height),
                                centerX: Math.round(rect.left + rect.width / 2),
                                centerY: Math.round(rect.top + rect.height / 2)
                            }
                        };
                        
                        if (style.position === 'fixed') {
                            info.elements_with_fixed_position.push(elementInfo);
                        } else {
                            info.elements_with_absolute_position.push(elementInfo);
                        }
                    }
                }
                
                return info;
            """)
            
            print(f"   ✅ CSS analysé - {len(css_info['elements_with_fixed_position'])} éléments fixes")
            return css_info
            
        except Exception as e:
            print(f"   ❌ Erreur CSS: {e}")
            return {}
    
    def _analyze_visual_elements(self):
        """Analyse visuelle avec capture d'écran"""
        try:
            # Prendre une capture d'écran
            screenshot = self.driver.get_screenshot_as_png()
            
            # Sauvegarder la capture
            with open('cloudflare_analysis_screenshot.png', 'wb') as f:
                f.write(screenshot)
            
            # Analyser les zones cliquables visuellement
            clickable_zones = self.driver.execute_script("""
                var zones = [];
                var clickableSelectors = [
                    'input', 'button', 'a', '[onclick]', '[role="button"]',
                    '.btn', '.button', '.click', '.checkbox'
                ];
                
                clickableSelectors.forEach(function(selector) {
                    try {
                        var elements = document.querySelectorAll(selector);
                        elements.forEach(function(element) {
                            if (element.offsetParent !== null) { // Visible
                                var rect = element.getBoundingClientRect();
                                if (rect.width > 5 && rect.height > 5) { // Taille raisonnable
                                    zones.push({
                                        selector: selector,
                                        tagName: element.tagName,
                                        text: (element.textContent || element.value || '').substring(0, 50),
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height),
                                            centerX: Math.round(rect.left + rect.width / 2),
                                            centerY: Math.round(rect.top + rect.height / 2)
                                        }
                                    });
                                }
                            }
                        });
                    } catch(e) {}
                });
                
                return zones;
            """)
            
            print(f"   ✅ Capture d'écran prise - {len(clickable_zones)} zones cliquables")
            print("   📸 Screenshot: cloudflare_analysis_screenshot.png")
            
            return {
                'screenshot_saved': True,
                'clickable_zones': clickable_zones
            }
            
        except Exception as e:
            print(f"   ❌ Erreur visuelle: {e}")
            return {}
    
    def _calculate_precise_coordinates(self):
        """Calcule les coordonnées précises basées sur l'analyse"""
        try:
            print("   🧮 Calcul des coordonnées précises...")
            
            # Analyser tous les résultats pour trouver la case à cocher
            candidates = []
            
            # Candidats des éléments Cloudflare
            if 'cloudflare_analysis' in self.analysis_results:
                for element in self.analysis_results['cloudflare_analysis']:
                    if (element['tagName'].lower() == 'input' and 
                        element.get('attributes', {}).get('type') == 'checkbox'):
                        candidates.append({
                            'source': 'cloudflare_analysis',
                            'confidence': 0.9,
                            'coordinates': (element['position']['centerX'], element['position']['centerY']),
                            'description': 'Case à cocher Cloudflare détectée'
                        })
            
            # Candidats des zones cliquables
            if 'visual_analysis' in self.analysis_results:
                for zone in self.analysis_results['visual_analysis'].get('clickable_zones', []):
                    if (zone['tagName'].lower() == 'input' or 
                        'checkbox' in zone.get('text', '').lower() or
                        'captcha' in zone.get('text', '').lower()):
                        candidates.append({
                            'source': 'visual_analysis',
                            'confidence': 0.7,
                            'coordinates': (zone['position']['centerX'], zone['position']['centerY']),
                            'description': f"Zone cliquable: {zone['text'][:30]}"
                        })
            
            # Coordonnées calculées basées sur les patterns Cloudflare standards
            standard_coordinates = [
                {'coordinates': (150, 300), 'confidence': 0.8, 'description': 'Position standard Cloudflare'},
                {'coordinates': (120, 280), 'confidence': 0.7, 'description': 'Position alternative Cloudflare'},
                {'coordinates': (180, 320), 'confidence': 0.6, 'description': 'Position décalée Cloudflare'}
            ]
            
            candidates.extend(standard_coordinates)
            
            # Trier par confiance
            candidates.sort(key=lambda x: x['confidence'], reverse=True)
            
            print(f"   ✅ {len(candidates)} coordonnées candidates calculées")
            
            # Afficher les meilleures coordonnées
            for i, candidate in enumerate(candidates[:5]):
                x, y = candidate['coordinates']
                conf = candidate['confidence']
                desc = candidate['description']
                print(f"   🎯 #{i+1}: ({x}, {y}) - Confiance: {conf:.1f} - {desc}")
            
            return candidates
            
        except Exception as e:
            print(f"   ❌ Erreur calcul: {e}")
            return []
    
    def test_calculated_coordinates(self):
        """Teste les coordonnées calculées"""
        if not self.analysis_results or 'calculated_coordinates' not in self.analysis_results:
            print("❌ Aucune coordonnée calculée à tester")
            return False
        
        print("\n🧪 TEST DES COORDONNÉES CALCULÉES")
        print("=" * 50)
        
        coordinates = self.analysis_results['calculated_coordinates']
        
        for i, candidate in enumerate(coordinates[:3]):  # Tester les 3 meilleures
            x, y = candidate['coordinates']
            conf = candidate['confidence']
            desc = candidate['description']
            
            print(f"\n🎯 Test #{i+1}: ({x}, {y}) - {desc}")
            print(f"   Confiance: {conf:.1f}")
            
            try:
                # Cliquer aux coordonnées
                self.driver.execute_script(f"""
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.dispatchEvent(event);
                        console.log('Clic effectué sur:', element);
                    }}
                """)
                
                time.sleep(3)
                
                # Vérifier si ça a fonctionné
                current_title = self.driver.title.lower()
                if not any(indicator in current_title for indicator in ["instant", "moment", "cloudflare"]):
                    print(f"   ✅ SUCCÈS! Coordonnées ({x}, {y}) fonctionnent!")
                    return True
                else:
                    print(f"   ⚠️ Pas de changement détecté")
                
            except Exception as e:
                print(f"   ❌ Erreur test: {e}")
        
        print("\n❌ Aucune coordonnée testée n'a fonctionné")
        return False

def main():
    """Fonction principale d'analyse expert"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🔬 ANALYSEUR EXPERT - CALCUL PRÉCIS CLOUDFLARE")
    print("=" * 70)
    print("👨‍💻 Expert en: DOM, CSS, Positionnement, Analyse Visuelle")
    print("🎯 Objectif: Calculer les coordonnées EXACTES de la case")
    print()
    
    analyzer = ExpertPageAnalyzer()
    
    # Analyse complète
    results = analyzer.analyze_cloudflare_page(url)
    
    if results:
        print("\n🎉 ANALYSE EXPERT TERMINÉE!")
        print("📊 Résultats disponibles dans: expert_analysis_results.json")
        print("📸 Capture d'écran: cloudflare_analysis_screenshot.png")
        
        # Test des coordonnées
        choice = input("\nTester les coordonnées calculées? (o/n): ")
        if choice.lower().strip() in ['o', 'oui', 'y', 'yes']:
            analyzer.test_calculated_coordinates()
    else:
        print("❌ Analyse échouée")

if __name__ == "__main__":
    main()
