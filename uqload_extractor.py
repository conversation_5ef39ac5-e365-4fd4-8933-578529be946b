#!/usr/bin/env python3
"""
Extracteur spécialisé pour UQload - French Stream
Automatise les clics sur uqload et extrait le lien vidéo direct
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import json
import re

class UQloadExtractor:
    """Extracteur spécialisé pour UQload sur French Stream"""
    
    def __init__(self, headless=False):
        self.headless = headless
        self.driver = None
        
    def setup_driver(self):
        """Configure le driver Selenium avec options anti-détection"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Options anti-détection
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # User agent réaliste
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Script anti-détection
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return True
    
    def extract_video_link(self, url):
        """Extrait le lien vidéo direct depuis French Stream"""
        print("🎬 Extracteur UQload - French Stream")
        print("=" * 50)
        print(f"🔗 URL: {url}")
        
        try:
            if not self.setup_driver():
                return None
            
            # Étape 1: Charger la page principale
            print("📡 Chargement de la page principale...")
            self.driver.get(url)
            
            # Attendre le chargement
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Attendre un peu plus pour le contenu dynamique
            time.sleep(5)
            
            print("✅ Page principale chargée")
            
            # Étape 2: Chercher le bouton UQload
            print("🔍 Recherche du bouton UQload...")
            
            uqload_selectors = [
                'a[href*="uqload"]',
                'button[onclick*="uqload"]',
                '[class*="uqload"]',
                'a:contains("uqload")',
                'button:contains("uqload")',
                '[data-server*="uqload"]',
                '.server-item[data-video*="uqload"]'
            ]
            
            uqload_button = None
            for selector in uqload_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.lower()
                        if 'uqload' in text or 'uqload' in element.get_attribute('href') or '':
                            uqload_button = element
                            print(f"✅ Bouton UQload trouvé: {selector}")
                            break
                    if uqload_button:
                        break
                except:
                    continue
            
            # Chercher aussi dans tous les liens et boutons
            if not uqload_button:
                print("🔍 Recherche élargie...")
                all_clickable = self.driver.find_elements(By.CSS_SELECTOR, "a, button, [onclick], [data-server]")
                
                for element in all_clickable:
                    try:
                        text = element.text.lower()
                        href = element.get_attribute('href') or ''
                        onclick = element.get_attribute('onclick') or ''
                        data_server = element.get_attribute('data-server') or ''
                        
                        if any('uqload' in attr.lower() for attr in [text, href, onclick, data_server]):
                            uqload_button = element
                            print(f"✅ Bouton UQload trouvé dans recherche élargie")
                            break
                    except:
                        continue
            
            if not uqload_button:
                print("❌ Bouton UQload non trouvé")
                return self.fallback_search()
            
            # Étape 3: Cliquer sur le bouton UQload
            print("🖱️ Clic sur le bouton UQload...")
            
            # Scroll vers l'élément
            self.driver.execute_script("arguments[0].scrollIntoView(true);", uqload_button)
            time.sleep(2)
            
            # Cliquer
            try:
                uqload_button.click()
            except:
                # Essayer avec JavaScript si le clic normal échoue
                self.driver.execute_script("arguments[0].click();", uqload_button)
            
            print("✅ Clic effectué")
            
            # Attendre le chargement du lecteur
            time.sleep(5)
            
            # Étape 4: Chercher le bouton play bleu
            print("🔍 Recherche du bouton play bleu...")
            
            play_selectors = [
                '.play-button',
                '[class*="play"]',
                'button[class*="play"]',
                '.video-play-button',
                '[onclick*="play"]',
                'button[style*="blue"]',
                '.btn-play',
                '#play-button'
            ]
            
            play_button = None
            for selector in play_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            play_button = element
                            print(f"✅ Bouton play trouvé: {selector}")
                            break
                    if play_button:
                        break
                except:
                    continue
            
            # Étape 5: Cliquer sur play et extraire le lien
            if play_button:
                print("🖱️ Clic sur le bouton play...")
                
                # Scroll vers le bouton play
                self.driver.execute_script("arguments[0].scrollIntoView(true);", play_button)
                time.sleep(2)
                
                try:
                    play_button.click()
                except:
                    self.driver.execute_script("arguments[0].click();", play_button)
                
                print("✅ Bouton play cliqué")
                
                # Attendre le chargement de la vidéo
                time.sleep(8)
            
            # Étape 6: Extraire le lien vidéo
            print("🎥 Extraction du lien vidéo...")
            
            video_links = self.extract_video_urls()
            
            if video_links:
                print(f"✅ {len(video_links)} lien(s) vidéo trouvé(s)!")
                
                result = {
                    'url': url,
                    'video_links': video_links,
                    'primary_link': video_links[0] if video_links else None,
                    'extraction_method': 'uqload_automation',
                    'success': True
                }
                
                # Sauvegarder les résultats
                with open('uqload_extraction.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print("💾 Résultats sauvegardés dans uqload_extraction.json")
                
                return result
            else:
                print("❌ Aucun lien vidéo trouvé")
                return None
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return None
        
        finally:
            if self.driver:
                self.driver.quit()
    
    def extract_video_urls(self):
        """Extrait les URLs vidéo de la page"""
        video_links = []
        
        try:
            # Méthode 1: Chercher les éléments video
            video_elements = self.driver.find_elements(By.TAG_NAME, "video")
            for video in video_elements:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_links.append(src)
                    print(f"📹 Lien vidéo (balise video): {src}")
                
                # Chercher les sources dans le video
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute('src')
                    if src and src.startswith('http'):
                        video_links.append(src)
                        print(f"📹 Lien vidéo (source): {src}")
            
            # Méthode 2: Analyser le code source pour les URLs vidéo
            page_source = self.driver.page_source
            
            # Patterns pour les liens vidéo
            video_patterns = [
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm|mkv|avi)',
                r'"(https?://[^"]+\.mp4[^"]*)"',
                r'"(https?://[^"]+\.m3u8[^"]*)"',
                r'src["\s]*:["\s]*"(https?://[^"]+)"',
                r'file["\s]*:["\s]*"(https?://[^"]+)"'
            ]
            
            for pattern in video_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0] if match[0] else match[1]
                    
                    if match and match.startswith('http') and match not in video_links:
                        video_links.append(match)
                        print(f"📹 Lien vidéo (regex): {match}")
            
            # Méthode 3: Intercepter les requêtes réseau (JavaScript)
            network_urls = self.driver.execute_script("""
                var urls = [];
                var originalFetch = window.fetch;
                var originalXHR = window.XMLHttpRequest.prototype.open;
                
                // Récupérer les URLs des requêtes récentes
                if (window.performance && window.performance.getEntriesByType) {
                    var entries = window.performance.getEntriesByType('resource');
                    for (var i = 0; i < entries.length; i++) {
                        var url = entries[i].name;
                        if (url.includes('.mp4') || url.includes('.m3u8') || url.includes('video')) {
                            urls.push(url);
                        }
                    }
                }
                
                return urls;
            """)
            
            for url in network_urls:
                if url not in video_links:
                    video_links.append(url)
                    print(f"📹 Lien vidéo (network): {url}")
            
        except Exception as e:
            print(f"⚠️ Erreur lors de l'extraction: {e}")
        
        return list(set(video_links))  # Supprimer les doublons
    
    def fallback_search(self):
        """Recherche de fallback si UQload n'est pas trouvé"""
        print("🔍 Recherche de fallback...")
        
        try:
            # Chercher tous les serveurs disponibles
            server_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                "a, button, [data-server], [onclick], .server-item, .player-server")
            
            servers_found = []
            for element in server_elements:
                text = element.text.strip()
                if text and len(text) < 50:  # Noms de serveurs courts
                    servers_found.append(text)
            
            if servers_found:
                print(f"📺 Serveurs trouvés: {', '.join(set(servers_found))}")
            
            return None
            
        except Exception as e:
            print(f"⚠️ Erreur fallback: {e}")
            return None

def main():
    """Fonction principale"""
    url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
    
    print("🎬 Extracteur UQload pour French Stream")
    print("=" * 60)
    print("🎯 Objectif: Trouver UQload → Cliquer Play → Extraire lien vidéo")
    print()
    
    # Demander si on veut voir le navigateur
    try:
        show_browser = input("Voulez-vous voir le navigateur en action? (o/n): ").lower().strip()
        headless = show_browser not in ['o', 'oui', 'y', 'yes']
    except:
        headless = True
    
    extractor = UQloadExtractor(headless=headless)
    result = extractor.extract_video_link(url)
    
    if result and result['success']:
        print("\n" + "="*60)
        print("🎉 EXTRACTION RÉUSSIE!")
        print("="*60)
        
        if result['primary_link']:
            print(f"🎯 LIEN VIDÉO PRINCIPAL:")
            print(f"   {result['primary_link']}")
        
        if len(result['video_links']) > 1:
            print(f"\n📹 TOUS LES LIENS VIDÉO ({len(result['video_links'])}):")
            for i, link in enumerate(result['video_links'], 1):
                print(f"   {i}. {link}")
        
        print(f"\n💾 Résultats sauvegardés dans: uqload_extraction.json")
        
    else:
        print("\n❌ Extraction échouée")
        print("💡 Suggestions:")
        print("   • Vérifiez que le film a bien un lecteur UQload")
        print("   • Essayez avec le navigateur visible (répondez 'o')")
        print("   • Le site peut avoir changé de structure")

if __name__ == "__main__":
    main()
