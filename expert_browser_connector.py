#!/usr/bin/env python3
"""
EXPERT BROWSER CONNECTOR
Expert en: Intégration navigateur existant, Remote debugging, Automation avancée
Se connecte au navigateur Chrome ouvert par l'utilisateur
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
import re
import subprocess
import psutil
import requests

class ExpertBrowserConnector:
    """Expert en connexion à navigateur existant"""
    
    def __init__(self):
        self.driver = None
        self.debug_port = None
        
    def find_chrome_debug_port(self):
        """Trouve le port de debug du Chrome ouvert"""
        print("🔍 Recherche du navigateur Chrome ouvert...")
        
        # Méthode 1: Chercher dans les processus Chrome
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        for arg in cmdline:
                            if '--remote-debugging-port=' in str(arg):
                                port = str(arg).split('=')[1]
                                print(f"   ✅ Port debug trouvé: {port}")
                                return int(port)
            except:
                continue
        
        # Méthode 2: Tester les ports standards
        standard_ports = [9222, 9223, 9224, 9225, 9226]
        for port in standard_ports:
            try:
                response = requests.get(f'http://localhost:{port}/json', timeout=2)
                if response.status_code == 200:
                    print(f"   ✅ Port debug actif trouvé: {port}")
                    return port
            except:
                continue
        
        return None
    
    def enable_chrome_debugging(self):
        """Active le debugging sur Chrome s'il n'est pas déjà actif"""
        print("🔧 Activation du debugging Chrome...")
        
        # Vérifier si déjà actif
        debug_port = self.find_chrome_debug_port()
        if debug_port:
            self.debug_port = debug_port
            return True
        
        # Essayer d'activer le debugging
        print("   ⚠️ Debugging non actif. Instructions pour l'activer:")
        print("   1. Fermez Chrome complètement")
        print("   2. Ouvrez une invite de commande")
        print("   3. Lancez: chrome.exe --remote-debugging-port=9222")
        print("   4. Ou utilisez le raccourci que je vais créer")
        
        # Créer un raccourci pour lancer Chrome avec debugging
        chrome_debug_cmd = '''
@echo off
echo Lancement de Chrome avec debugging...
start chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\\chrome_debug"
echo Chrome lancé avec debugging sur le port 9222
pause
'''
        
        with open('launch_chrome_debug.bat', 'w') as f:
            f.write(chrome_debug_cmd)
        
        print("   📄 Fichier créé: launch_chrome_debug.bat")
        print("   ▶️ Exécutez ce fichier pour lancer Chrome avec debugging")
        
        return False
    
    def connect_to_existing_chrome(self):
        """Se connecte au Chrome existant"""
        print("🔗 Connexion au navigateur Chrome existant...")
        
        if not self.debug_port:
            if not self.enable_chrome_debugging():
                return False
        
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            
            # Options pour éviter les conflits
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print("   ✅ Connexion réussie au navigateur existant!")
            print(f"   🌐 URL actuelle: {self.driver.current_url}")
            print(f"   📄 Titre: {self.driver.title}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur connexion: {e}")
            return False
    
    def expert_page_analysis(self):
        """Analyse experte de la page actuelle"""
        print("\n🔬 ANALYSE EXPERTE DE LA PAGE ACTUELLE")
        print("=" * 50)
        
        try:
            current_url = self.driver.current_url
            print(f"🔗 URL: {current_url}")
            print(f"📄 Titre: {self.driver.title}")
            
            # Analyse 1: Détection Cloudflare
            cloudflare_detected = self._detect_cloudflare()
            
            # Analyse 2: Recherche UQload
            uqload_elements = self._find_uqload_elements()
            
            # Analyse 3: Recherche boutons play
            play_buttons = self._find_play_buttons()
            
            # Analyse 4: Recherche vidéos
            video_elements = self._find_video_elements()
            
            return {
                'url': current_url,
                'cloudflare_detected': cloudflare_detected,
                'uqload_elements': uqload_elements,
                'play_buttons': play_buttons,
                'video_elements': video_elements
            }
            
        except Exception as e:
            print(f"❌ Erreur analyse: {e}")
            return None
    
    def _detect_cloudflare(self):
        """Détecte la présence de Cloudflare"""
        try:
            print("\n🛡️ Détection Cloudflare...")
            
            # Vérifier le titre
            title = self.driver.title.lower()
            if any(indicator in title for indicator in ["cloudflare", "checking", "moment", "instant"]):
                print("   ⚠️ Page de vérification Cloudflare détectée")
                return True
            
            # Vérifier les scripts
            scripts = self.driver.execute_script("""
                var cfScripts = 0;
                var scripts = document.querySelectorAll('script');
                for (var i = 0; i < scripts.length; i++) {
                    var src = scripts[i].src || '';
                    var content = scripts[i].innerHTML || '';
                    if (src.includes('cloudflare') || content.includes('cloudflare') ||
                        src.includes('cf-') || content.includes('cf-')) {
                        cfScripts++;
                    }
                }
                return cfScripts;
            """)
            
            if scripts > 0:
                print(f"   🔍 {scripts} scripts Cloudflare détectés")
                return True
            
            print("   ✅ Pas de Cloudflare détecté")
            return False
            
        except Exception as e:
            print(f"   ❌ Erreur détection Cloudflare: {e}")
            return False
    
    def _find_uqload_elements(self):
        """Trouve les éléments UQload"""
        try:
            print("\n🔍 Recherche éléments UQload...")
            
            uqload_elements = self.driver.execute_script("""
                var elements = [];
                var selectors = [
                    'a[href*="uqload" i]',
                    'button[onclick*="uqload" i]',
                    '[data-server*="uqload" i]',
                    '*[class*="uqload" i]',
                    '*[id*="uqload" i]',
                    '*:contains("uqload")',
                    '*:contains("UQload")'
                ];
                
                // Recherche par sélecteurs
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                elements.push({
                                    selector: selector,
                                    text: (element.textContent || element.innerText || '').substring(0, 50),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    },
                                    visible: true
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                // Recherche par texte
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                var textNode;
                while (textNode = walker.nextNode()) {
                    if (textNode.textContent.toLowerCase().includes('uqload')) {
                        var parent = textNode.parentElement;
                        if (parent && parent.offsetParent !== null) {
                            var rect = parent.getBoundingClientRect();
                            elements.push({
                                selector: 'text_search',
                                text: textNode.textContent.substring(0, 50),
                                position: {
                                    x: Math.round(rect.left + rect.width / 2),
                                    y: Math.round(rect.top + rect.height / 2)
                                },
                                visible: true
                            });
                        }
                    }
                }
                
                return elements;
            """)
            
            print(f"   📍 {len(uqload_elements)} éléments UQload trouvés")
            for i, element in enumerate(uqload_elements[:5]):  # Afficher les 5 premiers
                pos = element['position']
                text = element['text'].strip()
                print(f"   {i+1}. '{text}' à ({pos['x']}, {pos['y']})")
            
            return uqload_elements
            
        except Exception as e:
            print(f"   ❌ Erreur recherche UQload: {e}")
            return []
    
    def _find_play_buttons(self):
        """Trouve les boutons play"""
        try:
            print("\n▶️ Recherche boutons play...")
            
            play_buttons = self.driver.execute_script("""
                var buttons = [];
                var selectors = [
                    '.play-button',
                    '.video-play-button',
                    '[class*="play"]',
                    'button[class*="play"]',
                    '[onclick*="play"]',
                    '.vjs-big-play-button',
                    '[title*="play" i]',
                    '[alt*="play" i]'
                ];
                
                selectors.forEach(function(selector) {
                    try {
                        var found = document.querySelectorAll(selector);
                        found.forEach(function(element) {
                            if (element.offsetParent !== null) {
                                var rect = element.getBoundingClientRect();
                                buttons.push({
                                    selector: selector,
                                    text: (element.textContent || element.title || element.alt || '').substring(0, 30),
                                    position: {
                                        x: Math.round(rect.left + rect.width / 2),
                                        y: Math.round(rect.top + rect.height / 2)
                                    }
                                });
                            }
                        });
                    } catch(e) {}
                });
                
                return buttons;
            """)
            
            print(f"   ▶️ {len(play_buttons)} boutons play trouvés")
            for i, button in enumerate(play_buttons[:3]):
                pos = button['position']
                text = button['text'].strip()
                print(f"   {i+1}. '{text}' à ({pos['x']}, {pos['y']})")
            
            return play_buttons
            
        except Exception as e:
            print(f"   ❌ Erreur recherche play: {e}")
            return []
    
    def _find_video_elements(self):
        """Trouve les éléments vidéo"""
        try:
            print("\n🎥 Recherche éléments vidéo...")
            
            videos = self.driver.find_elements(By.TAG_NAME, "video")
            video_info = []
            
            for video in videos:
                src = video.get_attribute('src')
                if src:
                    video_info.append({
                        'src': src,
                        'type': 'direct'
                    })
            
            # Recherche dans le code source
            page_source = self.driver.page_source
            patterns = [
                r'"file":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'"src":\s*"([^"]+\.(?:mp4|m3u8|webm)[^"]*)"',
                r'https?://[^"\s]+\.(?:mp4|m3u8|webm)(?:\?[^"\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http'):
                        video_info.append({
                            'src': match,
                            'type': 'regex'
                        })
            
            # Supprimer les doublons
            unique_videos = []
            seen_urls = set()
            for video in video_info:
                if video['src'] not in seen_urls:
                    unique_videos.append(video)
                    seen_urls.add(video['src'])
            
            print(f"   🎬 {len(unique_videos)} liens vidéo trouvés")
            for i, video in enumerate(unique_videos[:3]):
                print(f"   {i+1}. {video['src'][:60]}...")
            
            return unique_videos
            
        except Exception as e:
            print(f"   ❌ Erreur recherche vidéo: {e}")
            return []
    
    def expert_interaction(self, analysis_results):
        """Interaction experte basée sur l'analyse"""
        print("\n🎯 INTERACTION EXPERTE")
        print("=" * 30)
        
        try:
            # Si Cloudflare détecté, attendre
            if analysis_results['cloudflare_detected']:
                print("⏳ Cloudflare détecté - Attente intelligente...")
                self._wait_for_cloudflare()
            
            # Cliquer sur UQload si trouvé
            if analysis_results['uqload_elements']:
                print("🖱️ Clic sur UQload...")
                success = self._click_uqload(analysis_results['uqload_elements'])
                if success:
                    time.sleep(5)
                    
                    # Nouvelle analyse après clic UQload
                    new_analysis = self.expert_page_analysis()
                    
                    # Cliquer sur play si trouvé
                    if new_analysis and new_analysis['play_buttons']:
                        print("▶️ Clic sur play...")
                        self._click_play(new_analysis['play_buttons'])
                        time.sleep(8)
                        
                        # Analyse finale pour vidéos
                        final_analysis = self.expert_page_analysis()
                        if final_analysis and final_analysis['video_elements']:
                            print("\n🎉 EXTRACTION RÉUSSIE!")
                            self._display_results(final_analysis['video_elements'])
                            return True
            
            # Si pas d'UQload, chercher directement les vidéos
            if analysis_results['video_elements']:
                print("🎬 Vidéos trouvées directement!")
                self._display_results(analysis_results['video_elements'])
                return True
            
            print("❌ Aucun élément exploitable trouvé")
            return False
            
        except Exception as e:
            print(f"❌ Erreur interaction: {e}")
            return False
    
    def _wait_for_cloudflare(self):
        """Attente intelligente pour Cloudflare"""
        max_wait = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                title = self.driver.title.lower()
                if not any(indicator in title for indicator in ["cloudflare", "checking", "moment", "instant"]):
                    print("   ✅ Cloudflare passé!")
                    return True
            except:
                pass
            
            time.sleep(2)
        
        print("   ⚠️ Timeout Cloudflare")
        return False
    
    def _click_uqload(self, uqload_elements):
        """Clique sur le premier élément UQload"""
        try:
            if not uqload_elements:
                return False
            
            element = uqload_elements[0]
            x, y = element['position']['x'], element['position']['y']
            
            print(f"   🎯 Clic UQload à ({x}, {y})")
            
            self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                    console.log('Clic UQload effectué');
                }}
            """)
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur clic UQload: {e}")
            return False
    
    def _click_play(self, play_buttons):
        """Clique sur le premier bouton play"""
        try:
            if not play_buttons:
                return False
            
            button = play_buttons[0]
            x, y = button['position']['x'], button['position']['y']
            
            print(f"   ▶️ Clic play à ({x}, {y})")
            
            self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                    console.log('Clic play effectué');
                }}
            """)
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erreur clic play: {e}")
            return False
    
    def _display_results(self, video_elements):
        """Affiche les résultats finaux"""
        print("\n🎬 LIENS VIDÉO EXTRAITS:")
        print("=" * 40)
        
        for i, video in enumerate(video_elements, 1):
            print(f"{i}. {video['src']}")
        
        if video_elements:
            # Sauvegarder les résultats
            result = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'url': self.driver.current_url,
                'video_links': [v['src'] for v in video_elements],
                'primary_link': video_elements[0]['src'],
                'extraction_method': 'expert_browser_connector',
                'success': True
            }
            
            with open('browser_connector_results.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Résultats sauvegardés: browser_connector_results.json")
            print(f"🎯 LIEN PRINCIPAL: {result['primary_link']}")
    
    def run_expert_test(self):
        """Lance le test expert complet"""
        print("🎓 EXPERT BROWSER CONNECTOR - TEST COMPLET")
        print("=" * 60)
        print("👨‍💻 Expert en: Intégration navigateur, Remote debugging")
        
        try:
            # Étape 1: Connexion au navigateur
            if not self.connect_to_existing_chrome():
                print("\n❌ Impossible de se connecter au navigateur")
                print("💡 Lancez Chrome avec: chrome.exe --remote-debugging-port=9222")
                return False
            
            # Étape 2: Analyse de la page
            analysis = self.expert_page_analysis()
            if not analysis:
                print("❌ Analyse de la page échouée")
                return False
            
            # Étape 3: Interaction experte
            success = self.expert_interaction(analysis)
            
            if success:
                print("\n🏆 TEST EXPERT RÉUSSI!")
                return True
            else:
                print("\n❌ Test expert échoué")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test expert: {e}")
            return False
        
        finally:
            # Ne pas fermer le navigateur (il appartient à l'utilisateur)
            print("\n📝 Navigateur laissé ouvert (appartient à l'utilisateur)")

def main():
    """Fonction principale"""
    print("🎓 EXPERT BROWSER CONNECTOR")
    print("=" * 50)
    print("🔗 Se connecte à votre navigateur Chrome ouvert")
    print("🎯 Exécute les tests directement dans votre navigateur")
    
    connector = ExpertBrowserConnector()
    success = connector.run_expert_test()
    
    if success:
        print("\n🎉 Mission accomplie!")
    else:
        print("\n💡 Assurez-vous que Chrome est ouvert avec debugging activé")

if __name__ == "__main__":
    main()
