#!/usr/bin/env python3
"""
FULLY AUTOMATIC EXTRACTOR - 100% AUTOMATIQUE
Expert en: Automation complète sans intervention
OBJECTIF: Extraire le lien vidéo ENTIÈREMENT automatiquement
"""

import subprocess
import time
import os
import sys
import json

def run_fully_automatic():
    """Extraction entièrement automatique"""
    print("🚀 FULLY AUTOMATIC EXTRACTOR")
    print("=" * 50)
    print("🎯 EXTRACTION 100% AUTOMATIQUE")
    print("🔥 ZÉRO INTERVENTION MANUELLE")
    print()
    
    # Créer le script d'automation complet
    automation_code = '''
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import re

def extract_automatically():
    print("🎯 DÉMARRAGE EXTRACTION AUTOMATIQUE")
    
    # Configuration Chrome automatique
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    
    driver = None
    try:
        # Lancer Chrome
        print("🚀 Lancement Chrome automatique...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # Navigation automatique
        url = "https://vvw.french-stream.bio/films/15120062-dis-moi-juste-que-tu-maimes.html"
        print(f"🌐 Navigation automatique vers: {url}")
        driver.get(url)
        
        # Attente Cloudflare automatique
        print("⏳ Attente Cloudflare automatique...")
        for i in range(30):
            try:
                title = driver.title.lower()
                if not any(word in title for word in ["cloudflare", "checking", "moment"]):
                    body = driver.find_element(By.TAG_NAME, "body")
                    if len(body.text) > 1000:
                        print("✅ Cloudflare passé automatiquement!")
                        break
            except:
                pass
            time.sleep(2)
            print(f"   ⏳ Attente... {i*2}s")
        
        # Recherche UQload automatique
        print("🔍 Recherche UQload automatique...")
        uqload_found = False
        
        # Méthode 1: Par texte
        try:
            elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'UQload') or contains(text(), 'uqload') or contains(text(), 'UQ')]")
            for element in elements:
                if element.is_displayed():
                    print(f"🎯 UQload trouvé automatiquement: {element.text[:30]}")
                    driver.execute_script("arguments[0].click();", element)
                    uqload_found = True
                    time.sleep(5)
                    break
        except:
            pass
        
        # Méthode 2: Par coordonnées probables
        if not uqload_found:
            print("🎯 Test coordonnées UQload automatique...")
            coords = [(400, 500), (350, 450), (450, 550), (300, 400)]
            for x, y in coords:
                try:
                    element = driver.execute_script(f"return document.elementFromPoint({x}, {y});")
                    if element:
                        text = element.get_attribute("textContent") or ""
                        if "uqload" in text.lower() or "uq" in text.lower():
                            print(f"🎯 UQload par coordonnées: ({x}, {y})")
                            driver.execute_script("arguments[0].click();", element)
                            uqload_found = True
                            time.sleep(5)
                            break
                except:
                    continue
        
        # Clic play automatique
        print("▶️ Clic play automatique...")
        time.sleep(8)
        
        # Méthode 1: Boutons play
        play_found = False
        try:
            selectors = [".play-button", ".video-play-button", "[class*='play']", "button[class*='play']"]
            for selector in selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        print(f"▶️ Play trouvé automatiquement: {selector}")
                        driver.execute_script("arguments[0].click();", element)
                        play_found = True
                        break
                if play_found:
                    break
        except:
            pass
        
        # Méthode 2: Clic centre automatique
        if not play_found:
            print("▶️ Clic centre automatique...")
            try:
                center_x = driver.execute_script("return window.innerWidth / 2;")
                center_y = driver.execute_script("return window.innerHeight / 2;")
                driver.execute_script(f"""
                    var element = document.elementFromPoint({center_x}, {center_y});
                    if (element) element.click();
                """)
                print("✅ Clic centre effectué automatiquement")
            except:
                pass
        
        time.sleep(10)
        
        # Extraction vidéo automatique
        print("🎥 Extraction vidéo automatique...")
        video_urls = []
        
        # Méthode 1: Éléments video
        try:
            videos = driver.find_elements(By.TAG_NAME, "video")
            for video in videos:
                src = video.get_attribute('src')
                if src and src.startswith('http'):
                    video_urls.append(src)
                    print(f"📹 Video automatique: {src[:60]}...")
                
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute('src')
                    if src and src.startswith('http'):
                        video_urls.append(src)
                        print(f"📹 Source automatique: {src[:60]}...")
        except:
            pass
        
        # Méthode 2: Regex automatique
        try:
            page_source = driver.page_source
            patterns = [
                r'"file":\\s*"([^"]+\\.mp4[^"]*)"',
                r'"src":\\s*"([^"]+\\.mp4[^"]*)"',
                r'"file":\\s*"([^"]+\\.m3u8[^"]*)"',
                r'"src":\\s*"([^"]+\\.m3u8[^"]*)"',
                r'https?://[^"\\s]+\\.mp4(?:\\?[^"\\s]*)?',
                r'https?://[^"\\s]+\\.m3u8(?:\\?[^"\\s]*)?'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if match and match.startswith('http') and match not in video_urls:
                        video_urls.append(match)
                        print(f"📹 Regex automatique: {match[:60]}...")
        except:
            pass
        
        # Méthode 3: JavaScript automatique
        try:
            js_videos = driver.execute_script("""
                var videos = [];
                for (var prop in window) {
                    try {
                        var value = window[prop];
                        if (typeof value === 'string' && 
                            (value.includes('.mp4') || value.includes('.m3u8')) &&
                            value.startsWith('http') && value.length < 500) {
                            videos.push(value);
                        }
                    } catch(e) {}
                }
                return videos;
            """)
            
            for js_video in js_videos:
                if js_video not in video_urls:
                    video_urls.append(js_video)
                    print(f"📹 JavaScript automatique: {js_video[:60]}...")
        except:
            pass
        
        # Supprimer doublons
        unique_videos = list(set(video_urls))
        
        # Résultats automatiques
        if unique_videos:
            # Trier par priorité
            def priority(url):
                if '.mp4' in url.lower():
                    return 1
                elif '.m3u8' in url.lower():
                    return 2
                else:
                    return 3
            
            unique_videos.sort(key=priority)
            
            result = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'url': url,
                'total_videos': len(unique_videos),
                'primary_video': unique_videos[0],
                'all_videos': unique_videos,
                'uqload_found': uqload_found,
                'method': 'fully_automatic',
                'success': True
            }
            
            # Sauvegarde automatique
            with open('fully_automatic_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print("\\n" + "="*70)
            print("🎉 EXTRACTION ENTIÈREMENT AUTOMATIQUE RÉUSSIE!")
            print("="*70)
            print(f"🎯 LIEN VIDÉO PRINCIPAL:")
            print(f"   {result['primary_video']}")
            
            if len(unique_videos) > 1:
                print(f"\\n📹 TOUS LES LIENS ({len(unique_videos)}):")
                for i, video in enumerate(unique_videos, 1):
                    video_type = "MP4" if ".mp4" in video.lower() else "M3U8" if ".m3u8" in video.lower() else "Autre"
                    print(f"   {i}. [{video_type}] {video}")
            
            print(f"\\n💾 Résultats: fully_automatic_result.json")
            print("="*70)
            
            return True
        else:
            print("\\n❌ AUCUNE VIDÉO TROUVÉE AUTOMATIQUEMENT")
            return False
    
    except Exception as e:
        print(f"\\n❌ ERREUR AUTOMATIQUE: {e}")
        return False
    
    finally:
        if driver:
            print("\\n⏳ Fermeture automatique dans 15 secondes...")
            time.sleep(15)
            driver.quit()
            print("✅ Navigateur fermé automatiquement")

if __name__ == "__main__":
    extract_automatically()
'''
    
    # Écrire le script d'automation
    with open('auto_extract.py', 'w', encoding='utf-8') as f:
        f.write(automation_code)
    
    print("📄 Script d'automation automatique créé: auto_extract.py")
    print("🚀 Lancement de l'extraction automatique...")
    print()
    
    # Lancer l'extraction automatique
    try:
        result = subprocess.run([sys.executable, 'auto_extract.py'], 
                              capture_output=True, text=True, 
                              cwd=os.getcwd(), timeout=300)
        
        print("📊 SORTIE DE L'EXTRACTION AUTOMATIQUE:")
        print("=" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ ERREURS:")
            print(result.stderr)
        
        # Vérifier si le fichier de résultat existe
        if os.path.exists('fully_automatic_result.json'):
            print("\\n🎉 FICHIER DE RÉSULTAT TROUVÉ!")
            with open('fully_automatic_result.json', 'r', encoding='utf-8') as f:
                auto_result = json.load(f)
            
            print("\\n🎯 RÉSULTAT FINAL AUTOMATIQUE:")
            print("=" * 40)
            print(f"✅ Succès: {auto_result['success']}")
            print(f"🎬 Lien principal: {auto_result['primary_video']}")
            print(f"📊 Total vidéos: {auto_result['total_videos']}")
            print(f"🔍 UQload trouvé: {auto_result['uqload_found']}")
            
            return True
        else:
            print("❌ Aucun fichier de résultat trouvé")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout - L'extraction prend plus de 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Erreur lancement: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 FULLY AUTOMATIC EXTRACTOR")
    print("=" * 60)
    print("🎯 EXTRACTION 100% AUTOMATIQUE")
    print("🔥 AUCUNE INTERVENTION MANUELLE")
    print("💪 TOUT SE FAIT AUTOMATIQUEMENT")
    print()
    
    success = run_fully_automatic()
    
    if success:
        print("\\n🏆 EXTRACTION ENTIÈREMENT AUTOMATIQUE RÉUSSIE!")
        print("🎬 Lien vidéo extrait sans intervention!")
    else:
        print("\\n❌ Extraction automatique échouée")
    
    print("\\n🔚 FIN DE L'EXTRACTION AUTOMATIQUE")

if __name__ == "__main__":
    main()
